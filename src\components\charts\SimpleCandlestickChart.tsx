import React from 'react';
import { CandlestickData } from '@/services/enhancedMarketDataService';

interface SimpleCandlestickChartProps {
  data: CandlestickData[];
  symbol: string;
  timeframe: string;
  height?: number;
  width?: number;
}

const SimpleCandlestickChart: React.FC<SimpleCandlestickChartProps> = ({
  data,
  symbol,
  timeframe,
  height = 400,
  width = 800
}) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center bg-slate-800 rounded-lg" style={{ height: `${height}px` }}>
        <div className="text-white">لا توجد بيانات للعرض</div>
      </div>
    );
  }

  // حساب القيم للرسم
  const prices = data.map(d => [d.open, d.high, d.low, d.close]).flat();
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);
  const priceRange = maxPrice - minPrice;
  const padding = priceRange * 0.1;

  const chartHeight = height - 80; // مساحة للعناوين
  const chartWidth = width - 100; // مساحة للمحاور
  const candleWidth = Math.max(2, chartWidth / data.length - 2);

  // تحويل السعر إلى إحداثي Y
  const priceToY = (price: number) => {
    return chartHeight - ((price - minPrice + padding) / (priceRange + 2 * padding)) * chartHeight;
  };

  // تحويل الفهرس إلى إحداثي X
  const indexToX = (index: number) => {
    return 50 + (index * (chartWidth / data.length)) + (candleWidth / 2);
  };

  return (
    <div className="bg-slate-800 rounded-lg p-4" style={{ width: `${width}px`, height: `${height}px` }}>
      {/* العنوان */}
      <div className="text-white text-lg font-bold mb-4 text-center">
        {symbol} - {timeframe}
      </div>

      {/* الرسم البياني */}
      <svg width={width - 32} height={height - 60} className="bg-slate-900 rounded">
        {/* خطوط الشبكة */}
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />

        {/* محور Y - الأسعار */}
        <g>
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, i) => {
            const price = minPrice + (priceRange * ratio);
            const y = priceToY(price);
            return (
              <g key={i}>
                <line x1="45" y1={y} x2={width - 50} y2={y} stroke="#374151" strokeWidth="1" opacity="0.5" />
                <text x="40" y={y + 4} fill="#9CA3AF" fontSize="10" textAnchor="end">
                  {price.toFixed(5)}
                </text>
              </g>
            );
          })}
        </g>

        {/* محور X - الوقت */}
        <g>
          {data.map((candle, index) => {
            if (index % Math.ceil(data.length / 8) === 0) {
              const x = indexToX(index);
              const date = new Date(candle.timestamp);
              const timeStr = date.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit' 
              });
              return (
                <g key={index}>
                  <line x1={x} y1={chartHeight} x2={x} y2={chartHeight + 5} stroke="#9CA3AF" strokeWidth="1" />
                  <text x={x} y={chartHeight + 18} fill="#9CA3AF" fontSize="9" textAnchor="middle">
                    {timeStr}
                  </text>
                </g>
              );
            }
            return null;
          })}
        </g>

        {/* الشموع اليابانية */}
        {data.map((candle, index) => {
          const x = indexToX(index);
          const openY = priceToY(candle.open);
          const closeY = priceToY(candle.close);
          const highY = priceToY(candle.high);
          const lowY = priceToY(candle.low);

          const isBullish = candle.close > candle.open;
          const bodyTop = Math.min(openY, closeY);
          const bodyBottom = Math.max(openY, closeY);
          const bodyHeight = Math.abs(closeY - openY);

          const color = isBullish ? '#10B981' : '#EF4444';
          const fillColor = isBullish ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)';

          return (
            <g key={index}>
              {/* الذيل العلوي */}
              <line
                x1={x}
                y1={highY}
                x2={x}
                y2={bodyTop}
                stroke={color}
                strokeWidth="1"
              />
              
              {/* الذيل السفلي */}
              <line
                x1={x}
                y1={bodyBottom}
                x2={x}
                y2={lowY}
                stroke={color}
                strokeWidth="1"
              />
              
              {/* جسم الشمعة */}
              <rect
                x={x - candleWidth / 2}
                y={bodyTop}
                width={candleWidth}
                height={Math.max(1, bodyHeight)}
                fill={fillColor}
                stroke={color}
                strokeWidth="1"
              />

              {/* نقطة للتفاعل */}
              <circle
                cx={x}
                cy={closeY}
                r="2"
                fill={color}
                opacity="0"
                className="hover:opacity-100 cursor-pointer"
              >
                <title>
                  {`الوقت: ${new Date(candle.timestamp).toLocaleString('ar-SA')}
فتح: ${candle.open.toFixed(5)}
أعلى: ${candle.high.toFixed(5)}
أقل: ${candle.low.toFixed(5)}
إغلاق: ${candle.close.toFixed(5)}
الحجم: ${(candle.volume / 1000000).toFixed(2)}M`}
                </title>
              </circle>
            </g>
          );
        })}

        {/* خط الاتجاه العام */}
        {data.length > 1 && (
          <line
            x1={indexToX(0)}
            y1={priceToY(data[0].close)}
            x2={indexToX(data.length - 1)}
            y2={priceToY(data[data.length - 1].close)}
            stroke="#3B82F6"
            strokeWidth="2"
            strokeDasharray="5,5"
            opacity="0.6"
          />
        )}
      </svg>

      {/* معلومات إضافية */}
      <div className="flex justify-between items-center mt-2 text-sm text-gray-400">
        <div>
          عدد الشموع: {data.length}
        </div>
        <div>
          المدى: {minPrice.toFixed(5)} - {maxPrice.toFixed(5)}
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>صعود</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>هبوط</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleCandlestickChart;
