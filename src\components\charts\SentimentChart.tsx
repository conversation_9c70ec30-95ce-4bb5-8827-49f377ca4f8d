import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';
import { SentimentHistory } from '@/services/socialSentimentService';

interface SentimentChartProps {
  data: SentimentHistory;
  width?: number;
  height?: number;
  theme?: 'light' | 'dark';
}

const SentimentChart: React.FC<SentimentChartProps> = ({
  data,
  width = 800,
  height = 400,
  theme = 'dark'
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (data && data.data.length > 0) {
      drawChart();
    }
  }, [data, theme]);

  const drawChart = () => {
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // إعداد الألوان
    const colors = theme === 'dark' ? {
      background: '#0F172A',
      grid: '#334155',
      text: '#F1F5F9',
      positive: '#10B981',
      negative: '#EF4444',
      neutral: '#F59E0B',
      line: '#3B82F6',
      area: 'rgba(59, 130, 246, 0.1)'
    } : {
      background: '#FFFFFF',
      grid: '#E2E8F0',
      text: '#1E293B',
      positive: '#059669',
      negative: '#DC2626',
      neutral: '#D97706',
      line: '#2563EB',
      area: 'rgba(37, 99, 235, 0.1)'
    };

    // إعداد الأبعاد
    const margin = { top: 20, right: 50, bottom: 50, left: 50 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // خلفية الرسم
    svg.attr('width', width)
       .attr('height', height)
       .style('background-color', colors.background);

    // المقاييس
    const xScale = d3.scaleTime()
      .domain(d3.extent(data.data, d => new Date(d.timestamp)) as [Date, Date])
      .range([0, chartWidth]);

    const yScale = d3.scaleLinear()
      .domain([-1, 1])
      .range([chartHeight, 0]);

    const volumeScale = d3.scaleLinear()
      .domain([0, d3.max(data.data, d => d.volume) || 100])
      .range([chartHeight, chartHeight * 0.7]);

    // المجموعة الرئيسية
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // خطوط الشبكة
    const xAxis = d3.axisBottom(xScale)
      .tickFormat(d3.timeFormat('%H:%M'));

    const yAxis = d3.axisLeft(yScale)
      .tickFormat(d => `${(d as number * 100).toFixed(0)}%`);

    // رسم المحاور
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${chartHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '12px');

    g.append('g')
      .attr('class', 'y-axis')
      .call(yAxis)
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '12px');

    // خطوط الشبكة
    g.selectAll('.grid-line-x')
      .data(xScale.ticks(8))
      .enter()
      .append('line')
      .attr('class', 'grid-line-x')
      .attr('x1', d => xScale(d))
      .attr('x2', d => xScale(d))
      .attr('y1', 0)
      .attr('y2', chartHeight)
      .style('stroke', colors.grid)
      .style('stroke-width', 1)
      .style('opacity', 0.3);

    g.selectAll('.grid-line-y')
      .data(yScale.ticks(6))
      .enter()
      .append('line')
      .attr('class', 'grid-line-y')
      .attr('x1', 0)
      .attr('x2', chartWidth)
      .attr('y1', d => yScale(d))
      .attr('y2', d => yScale(d))
      .style('stroke', colors.grid)
      .style('stroke-width', 1)
      .style('opacity', 0.3);

    // خط الصفر
    g.append('line')
      .attr('x1', 0)
      .attr('x2', chartWidth)
      .attr('y1', yScale(0))
      .attr('y2', yScale(0))
      .style('stroke', colors.text)
      .style('stroke-width', 2)
      .style('opacity', 0.5)
      .style('stroke-dasharray', '5,5');

    // منطقة المشاعر الإيجابية
    const positiveArea = d3.area<any>()
      .x(d => xScale(new Date(d.timestamp)))
      .y0(yScale(0))
      .y1(d => d.sentiment > 0 ? yScale(d.sentiment) : yScale(0))
      .curve(d3.curveMonotoneX);

    g.append('path')
      .datum(data.data)
      .attr('fill', colors.positive)
      .attr('opacity', 0.3)
      .attr('d', positiveArea);

    // منطقة المشاعر السلبية
    const negativeArea = d3.area<any>()
      .x(d => xScale(new Date(d.timestamp)))
      .y0(yScale(0))
      .y1(d => d.sentiment < 0 ? yScale(d.sentiment) : yScale(0))
      .curve(d3.curveMonotoneX);

    g.append('path')
      .datum(data.data)
      .attr('fill', colors.negative)
      .attr('opacity', 0.3)
      .attr('d', negativeArea);

    // خط المشاعر الرئيسي
    const line = d3.line<any>()
      .x(d => xScale(new Date(d.timestamp)))
      .y(d => yScale(d.sentiment))
      .curve(d3.curveMonotoneX);

    g.append('path')
      .datum(data.data)
      .attr('fill', 'none')
      .attr('stroke', colors.line)
      .attr('stroke-width', 3)
      .attr('d', line);

    // نقاط البيانات
    g.selectAll('.data-point')
      .data(data.data)
      .enter()
      .append('circle')
      .attr('class', 'data-point')
      .attr('cx', d => xScale(new Date(d.timestamp)))
      .attr('cy', d => yScale(d.sentiment))
      .attr('r', 4)
      .attr('fill', d => d.sentiment > 0.2 ? colors.positive : 
                       d.sentiment < -0.2 ? colors.negative : colors.neutral)
      .attr('stroke', colors.background)
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        // إظهار التولتيب
        const tooltip = d3.select('body').append('div')
          .attr('class', 'sentiment-tooltip')
          .style('position', 'absolute')
          .style('background', colors.background)
          .style('border', `1px solid ${colors.grid}`)
          .style('border-radius', '4px')
          .style('padding', '10px')
          .style('color', colors.text)
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0)
          .style('z-index', 1000);

        tooltip.html(`
          <div><strong>الوقت:</strong> ${new Date(d.timestamp).toLocaleString('ar-SA')}</div>
          <div><strong>المشاعر:</strong> ${(d.sentiment * 100).toFixed(1)}%</div>
          <div><strong>الحجم:</strong> ${d.volume}</div>
          ${d.events.length > 0 ? `<div><strong>الأحداث:</strong> ${d.events.join(', ')}</div>` : ''}
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px')
        .transition()
        .duration(200)
        .style('opacity', 1);

        // تمييز النقطة
        d3.select(this)
          .transition()
          .duration(200)
          .attr('r', 6);
      })
      .on('mouseout', function() {
        // إخفاء التولتيب
        d3.selectAll('.sentiment-tooltip').remove();
        
        // إزالة التمييز
        d3.select(this)
          .transition()
          .duration(200)
          .attr('r', 4);
      });

    // أعمدة الحجم (في الخلفية)
    g.selectAll('.volume-bar')
      .data(data.data)
      .enter()
      .append('rect')
      .attr('class', 'volume-bar')
      .attr('x', d => xScale(new Date(d.timestamp)) - 2)
      .attr('y', d => volumeScale(d.volume))
      .attr('width', 4)
      .attr('height', d => chartHeight - volumeScale(d.volume))
      .attr('fill', colors.grid)
      .attr('opacity', 0.3);

    // تسميات المحاور
    g.append('text')
      .attr('transform', 'rotate(-90)')
      .attr('y', 0 - margin.left)
      .attr('x', 0 - (chartHeight / 2))
      .attr('dy', '1em')
      .style('text-anchor', 'middle')
      .style('fill', colors.text)
      .style('font-size', '14px')
      .text('مؤشر المشاعر (%)');

    g.append('text')
      .attr('transform', `translate(${chartWidth / 2}, ${chartHeight + margin.bottom - 10})`)
      .style('text-anchor', 'middle')
      .style('fill', colors.text)
      .style('font-size', '14px')
      .text('الوقت');

    // العنوان
    svg.append('text')
      .attr('x', width / 2)
      .attr('y', 20)
      .attr('text-anchor', 'middle')
      .style('fill', colors.text)
      .style('font-size', '16px')
      .style('font-weight', 'bold')
      .text(`تاريخ المشاعر - ${data.symbol}`);

    // مؤشرات الألوان
    const legend = svg.append('g')
      .attr('transform', `translate(${width - 150}, 30)`);

    const legendData = [
      { color: colors.positive, label: 'إيجابي' },
      { color: colors.negative, label: 'سلبي' },
      { color: colors.neutral, label: 'محايد' }
    ];

    legendData.forEach((item, i) => {
      const legendItem = legend.append('g')
        .attr('transform', `translate(0, ${i * 20})`);

      legendItem.append('circle')
        .attr('r', 6)
        .attr('fill', item.color);

      legendItem.append('text')
        .attr('x', 15)
        .attr('y', 4)
        .style('fill', colors.text)
        .style('font-size', '12px')
        .text(item.label);
    });

    // معلومات الارتباط
    const correlationInfo = svg.append('g')
      .attr('transform', `translate(20, ${height - 80})`);

    correlationInfo.append('text')
      .style('fill', colors.text)
      .style('font-size', '12px')
      .style('font-weight', 'bold')
      .text('الارتباطات:');

    correlationInfo.append('text')
      .attr('y', 15)
      .style('fill', colors.text)
      .style('font-size', '11px')
      .text(`السعر: ${(data.correlations.priceCorrelation * 100).toFixed(0)}%`);

    correlationInfo.append('text')
      .attr('y', 30)
      .style('fill', colors.text)
      .style('font-size', '11px')
      .text(`الحجم: ${(data.correlations.volumeCorrelation * 100).toFixed(0)}%`);

    correlationInfo.append('text')
      .attr('y', 45)
      .style('fill', colors.text)
      .style('font-size', '11px')
      .text(`التقلبات: ${(data.correlations.volatilityCorrelation * 100).toFixed(0)}%`);
  };

  return (
    <div className="relative">
      <svg ref={svgRef} className="border border-slate-600 rounded-lg"></svg>
    </div>
  );
};

export default SentimentChart;
