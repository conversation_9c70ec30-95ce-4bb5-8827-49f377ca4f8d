import React, { useState, useEffect } from 'react';
import { <PERSON>fresh<PERSON><PERSON>, Brain, TrendingUp, AlertCircle, CheckCircle, Clock, Zap, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  continuousLearningService, 
  ModelPerformance, 
  LearningMetrics,
  RetrainingTrigger 
} from '@/services/continuousLearningService';

interface EnhancedContinuousLearningProps {
  lang?: 'en' | 'ar';
}

const EnhancedContinuousLearning: React.FC<EnhancedContinuousLearningProps> = ({ lang = 'ar' }) => {
  const [performanceMetrics, setPerformanceMetrics] = useState<ModelPerformance[]>([]);
  const [learningStatus, setLearningStatus] = useState<any>(null);
  const [systemMetrics, setSystemMetrics] = useState<any>(null);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [modelLearningMetrics, setModelLearningMetrics] = useState<LearningMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadLearningData();
    const interval = setInterval(loadLearningData, 30000); // تحديث كل 30 ثانية
    return () => clearInterval(interval);
  }, []);

  const loadLearningData = async () => {
    try {
      // جلب مقاييس الأداء
      const metrics = continuousLearningService.getPerformanceMetrics();
      setPerformanceMetrics(metrics);

      // جلب حالة التعلم
      const status = continuousLearningService.getLearningStatus();
      setLearningStatus(status);

      // جلب مقاييس النظام
      const systemData = await continuousLearningService.getContinuousLearningMetrics();
      setSystemMetrics(systemData);

      // إذا كان هناك نموذج محدد، جلب مقاييس التعلم الخاصة به
      if (selectedModel) {
        try {
          const learningMetrics = continuousLearningService.getLearningMetrics(selectedModel);
          setModelLearningMetrics(learningMetrics);
        } catch (error) {
          console.error('Error loading learning metrics:', error);
        }
      }

    } catch (error) {
      console.error('Error loading learning data:', error);
    }
  };

  const handleForceRetraining = async (modelId: string) => {
    setIsLoading(true);
    try {
      await continuousLearningService.forceRetraining(modelId);
      await loadLearningData();
    } catch (error) {
      console.error('Error forcing retraining:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleModelSelect = async (modelId: string) => {
    setSelectedModel(modelId);
    try {
      const learningMetrics = continuousLearningService.getLearningMetrics(modelId);
      setModelLearningMetrics(learningMetrics);
    } catch (error) {
      console.error('Error loading model metrics:', error);
      setModelLearningMetrics(null);
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'warning': return 'text-yellow-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'continue': return 'default';
      case 'retrain': return 'destructive';
      case 'adjust_params': return 'secondary';
      case 'collect_data': return 'outline';
      default: return 'outline';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'continue': return lang === 'ar' ? 'متابعة' : 'Continue';
      case 'retrain': return lang === 'ar' ? 'إعادة تدريب' : 'Retrain';
      case 'adjust_params': return lang === 'ar' ? 'ضبط المعاملات' : 'Adjust Parameters';
      case 'collect_data': return lang === 'ar' ? 'جمع بيانات' : 'Collect Data';
      default: return action;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* العنوان الرئيسي */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <RefreshCw className="h-8 w-8 text-blue-400" />
            {lang === 'ar' ? 'التعلم المستمر المحسن' : 'Enhanced Continuous Learning'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar' 
              ? 'نظام متقدم للتعلم المستمر مع مراقبة الأداء وإعادة التدريب التلقائي'
              : 'Advanced continuous learning system with performance monitoring and automatic retraining'
            }
          </p>
        </div>

        {/* إحصائيات النظام */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Brain className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'النماذج النشطة' : 'Active Models'}
              </h3>
              <p className="text-2xl font-bold text-blue-400">
                {systemMetrics ? `${systemMetrics.activeModels}/${systemMetrics.totalModels}` : '3/3'}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'متوسط الدقة' : 'Average Accuracy'}
              </h3>
              <p className="text-2xl font-bold text-green-400">
                {systemMetrics ? `${(systemMetrics.averageAccuracy * 100).toFixed(1)}%` : '89.2%'}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Clock className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'طابور التعلم' : 'Learning Queue'}
              </h3>
              <p className="text-2xl font-bold text-purple-400">
                {learningStatus ? learningStatus.queueSize : '0'}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <div className={`h-12 w-12 mx-auto mb-4 flex items-center justify-center rounded-full ${
                systemMetrics?.systemHealth === 'excellent' ? 'bg-green-500' :
                systemMetrics?.systemHealth === 'good' ? 'bg-blue-500' :
                systemMetrics?.systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'صحة النظام' : 'System Health'}
              </h3>
              <p className={`text-2xl font-bold ${getHealthColor(systemMetrics?.systemHealth || 'good')}`}>
                {systemMetrics?.systemHealth === 'excellent' ? (lang === 'ar' ? 'ممتاز' : 'Excellent') :
                 systemMetrics?.systemHealth === 'good' ? (lang === 'ar' ? 'جيد' : 'Good') :
                 systemMetrics?.systemHealth === 'warning' ? (lang === 'ar' ? 'تحذير' : 'Warning') :
                 (lang === 'ar' ? 'حرج' : 'Critical')}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-center gap-4">
          <Button onClick={loadLearningData} disabled={isLoading} className="flex items-center gap-2">
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? (lang === 'ar' ? 'جاري التحديث...' : 'Updating...') : (lang === 'ar' ? 'تحديث البيانات' : 'Refresh Data')}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* مقاييس أداء النماذج */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-400" />
                {lang === 'ar' ? 'أداء النماذج' : 'Model Performance'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {performanceMetrics.length > 0 ? (
                  performanceMetrics.map((model, index) => (
                    <div 
                      key={index} 
                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                        selectedModel === model.modelId 
                          ? 'bg-blue-900/30 border-blue-400' 
                          : 'bg-slate-700 border-slate-600 hover:border-slate-500'
                      }`}
                      onClick={() => handleModelSelect(model.modelId)}
                    >
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-white">{model.modelName}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">
                            {(model.accuracy * 100).toFixed(1)}%
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleForceRetraining(model.modelId);
                            }}
                            disabled={isLoading}
                            className="h-6 px-2 text-xs"
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">{lang === 'ar' ? 'الدقة:' : 'Accuracy:'}</span>
                          <div className="mt-1">
                            <Progress value={model.accuracy * 100} className="h-2" />
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-400">{lang === 'ar' ? 'الخسارة:' : 'Loss:'}</span>
                          <div className="mt-1">
                            <Progress value={(1 - model.loss) * 100} className="h-2" />
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-3 flex justify-between text-xs text-gray-400">
                        <span>{lang === 'ar' ? 'العصور:' : 'Epochs:'} {model.trainingEpochs}</span>
                        <span>{lang === 'ar' ? 'البيانات:' : 'Data:'} {model.dataPoints}</span>
                        <span>{lang === 'ar' ? 'آخر تحديث:' : 'Updated:'} {new Date(model.lastUpdated).toLocaleTimeString()}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-400">
                      {lang === 'ar' ? 'لا توجد نماذج متاحة' : 'No models available'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* تفاصيل النموذج المحدد */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-green-400" />
                {lang === 'ar' ? 'تفاصيل التعلم' : 'Learning Details'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedModel && modelLearningMetrics ? (
                <div className="space-y-6">
                  {/* مقاييس التعلم */}
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">{lang === 'ar' ? 'معدل التقارب:' : 'Convergence Rate:'}</span>
                        <span className="text-white">{(modelLearningMetrics.convergenceRate * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={modelLearningMetrics.convergenceRate * 100} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">{lang === 'ar' ? 'درجة الاستقرار:' : 'Stability Score:'}</span>
                        <span className="text-white">{(modelLearningMetrics.stabilityScore * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={modelLearningMetrics.stabilityScore * 100} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">{lang === 'ar' ? 'القدرة على التكيف:' : 'Adaptability:'}</span>
                        <span className="text-white">{(modelLearningMetrics.adaptabilityScore * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={modelLearningMetrics.adaptabilityScore * 100} className="h-2" />
                    </div>
                    
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">{lang === 'ar' ? 'خطر الإفراط:' : 'Overfitting Risk:'}</span>
                        <span className="text-white">{(modelLearningMetrics.overfittingRisk * 100).toFixed(1)}%</span>
                      </div>
                      <Progress value={modelLearningMetrics.overfittingRisk * 100} className="h-2" />
                    </div>
                  </div>

                  {/* الإجراء الموصى به */}
                  <div className="p-4 bg-slate-700 rounded-lg">
                    <h4 className="font-medium text-white mb-2">
                      {lang === 'ar' ? 'الإجراء الموصى به:' : 'Recommended Action:'}
                    </h4>
                    <Badge variant={getActionColor(modelLearningMetrics.recommendedAction)}>
                      {getActionText(modelLearningMetrics.recommendedAction)}
                    </Badge>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">
                    {lang === 'ar' ? 'اختر نموذجاً لعرض التفاصيل' : 'Select a model to view details'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* محفزات إعادة التدريب */}
        {learningStatus?.recentTriggers && learningStatus.recentTriggers.length > 0 && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-400" />
                {lang === 'ar' ? 'محفزات إعادة التدريب الأخيرة' : 'Recent Retraining Triggers'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {learningStatus.recentTriggers.map((trigger: RetrainingTrigger, index: number) => (
                  <div key={index} className="p-3 bg-slate-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-white">{trigger.type.replace('_', ' ').toUpperCase()}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant={trigger.severity === 'critical' ? 'destructive' : trigger.severity === 'high' ? 'secondary' : 'outline'}>
                          {trigger.severity}
                        </Badge>
                        {trigger.actionTaken && (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-300 mb-2">{trigger.description}</p>
                    <div className="text-xs text-gray-400">
                      {new Date(trigger.timestamp).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* حالة النظام */}
        {learningStatus && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-400" />
                {lang === 'ar' ? 'حالة النظام' : 'System Status'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-3 ${
                    learningStatus.isActive ? 'bg-green-500' : 'bg-gray-500'
                  }`}>
                    <RefreshCw className={`h-8 w-8 text-white ${learningStatus.isActive ? 'animate-spin' : ''}`} />
                  </div>
                  <h4 className="font-medium text-white mb-1">
                    {lang === 'ar' ? 'حالة التعلم' : 'Learning Status'}
                  </h4>
                  <p className="text-sm text-gray-400">
                    {learningStatus.isActive 
                      ? (lang === 'ar' ? 'نشط' : 'Active')
                      : (lang === 'ar' ? 'خامل' : 'Idle')
                    }
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400 mb-2">
                    {learningStatus.queueSize}
                  </div>
                  <h4 className="font-medium text-white mb-1">
                    {lang === 'ar' ? 'المهام في الطابور' : 'Queued Tasks'}
                  </h4>
                  <p className="text-sm text-gray-400">
                    {learningStatus.currentTask || (lang === 'ar' ? 'لا توجد مهام' : 'No current task')}
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {learningStatus.activeModels}
                  </div>
                  <h4 className="font-medium text-white mb-1">
                    {lang === 'ar' ? 'النماذج النشطة' : 'Active Models'}
                  </h4>
                  <p className="text-sm text-gray-400">
                    {lang === 'ar' ? `من أصل ${learningStatus.totalModels}` : `out of ${learningStatus.totalModels}`}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default EnhancedContinuousLearning;
