import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';
import { CandlestickData, TechnicalIndicator } from '@/services/enhancedMarketDataService';

interface D3CandlestickChartProps {
  data: CandlestickData[];
  indicators?: TechnicalIndicator[];
  supportLevels?: number[];
  resistanceLevels?: number[];
  symbol: string;
  timeframe: string;
  height?: number;
  width?: number;
  showVolume?: boolean;
  theme?: 'light' | 'dark';
}

const D3CandlestickChart: React.FC<D3CandlestickChartProps> = ({
  data,
  indicators = [],
  supportLevels = [],
  resistanceLevels = [],
  symbol,
  timeframe,
  height = 600,
  width = 1200,
  showVolume = true,
  theme = 'dark'
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (data && data.length > 0) {
      drawChart();
    }
  }, [data, indicators, supportLevels, resistanceLevels, theme]);

  const drawChart = () => {
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // مسح الرسم السابق

    // إعداد الألوان
    const colors = theme === 'dark' ? {
      background: '#0F172A',
      grid: '#334155',
      text: '#F1F5F9',
      bullish: '#10B981',
      bearish: '#EF4444',
      volume: '#64748B'
    } : {
      background: '#FFFFFF',
      grid: '#E2E8F0',
      text: '#1E293B',
      bullish: '#059669',
      bearish: '#DC2626',
      volume: '#64748B'
    };

    // إعداد الأبعاد
    const margin = { top: 50, right: 50, bottom: showVolume ? 150 : 50, left: 80 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;
    const volumeHeight = showVolume ? 80 : 0;
    const priceHeight = chartHeight - volumeHeight - (showVolume ? 20 : 0);

    // خلفية الرسم
    svg.attr('width', width)
       .attr('height', height)
       .style('background-color', colors.background);

    // حساب النطاقات
    const prices = data.map(d => [d.open, d.high, d.low, d.close]).flat();
    const minPrice = d3.min(prices) || 0;
    const maxPrice = d3.max(prices) || 0;
    const maxVolume = d3.max(data, d => d.volume) || 0;

    // المقاييس
    const xScale = d3.scaleLinear()
      .domain([0, data.length - 1])
      .range([0, chartWidth]);

    const yScale = d3.scaleLinear()
      .domain([minPrice * 0.999, maxPrice * 1.001])
      .range([priceHeight, 0]);

    const volumeScale = d3.scaleLinear()
      .domain([0, maxVolume])
      .range([volumeHeight, 0]);

    // المجموعة الرئيسية
    const g = svg.append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // الشبكة
    const xAxis = d3.axisBottom(xScale)
      .tickFormat((d, i) => {
        const index = d as number;
        if (index % Math.ceil(data.length / 8) === 0 && data[index]) {
          const date = new Date(data[index].timestamp);
          return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
        }
        return '';
      });

    const yAxis = d3.axisRight(yScale)
      .tickFormat(d => (d as number).toFixed(5));

    // رسم المحاور
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${priceHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '10px');

    g.append('g')
      .attr('class', 'y-axis')
      .attr('transform', `translate(${chartWidth},0)`)
      .call(yAxis)
      .selectAll('text')
      .style('fill', colors.text)
      .style('font-size', '12px');

    // خطوط الشبكة
    g.selectAll('.grid-line-x')
      .data(xScale.ticks(8))
      .enter()
      .append('line')
      .attr('class', 'grid-line-x')
      .attr('x1', d => xScale(d))
      .attr('x2', d => xScale(d))
      .attr('y1', 0)
      .attr('y2', priceHeight)
      .style('stroke', colors.grid)
      .style('stroke-width', 1)
      .style('opacity', 0.3);

    g.selectAll('.grid-line-y')
      .data(yScale.ticks(10))
      .enter()
      .append('line')
      .attr('class', 'grid-line-y')
      .attr('x1', 0)
      .attr('x2', chartWidth)
      .attr('y1', d => yScale(d))
      .attr('y2', d => yScale(d))
      .style('stroke', colors.grid)
      .style('stroke-width', 1)
      .style('opacity', 0.3);

    // مستويات الدعم والمقاومة
    supportLevels.forEach(level => {
      g.append('line')
        .attr('x1', 0)
        .attr('x2', chartWidth)
        .attr('y1', yScale(level))
        .attr('y2', yScale(level))
        .style('stroke', colors.bullish)
        .style('stroke-width', 2)
        .style('stroke-dasharray', '5,5')
        .style('opacity', 0.7);
    });

    resistanceLevels.forEach(level => {
      g.append('line')
        .attr('x1', 0)
        .attr('x2', chartWidth)
        .attr('y1', yScale(level))
        .attr('y2', yScale(level))
        .style('stroke', colors.bearish)
        .style('stroke-width', 2)
        .style('stroke-dasharray', '5,5')
        .style('opacity', 0.7);
    });

    // المؤشرات الفنية
    indicators.forEach(indicator => {
      if (indicator.type === 'line' && indicator.values.length > 0) {
        const line = d3.line<number>()
          .x((d, i) => xScale(i))
          .y(d => yScale(d))
          .defined(d => !isNaN(d))
          .curve(d3.curveLinear);

        g.append('path')
          .datum(indicator.values.slice(0, data.length))
          .attr('fill', 'none')
          .attr('stroke', indicator.color)
          .attr('stroke-width', 2)
          .attr('opacity', 0.8)
          .attr('d', line);
      }
    });

    // الشموع اليابانية
    const candleWidth = Math.max(2, chartWidth / data.length - 2);

    const candles = g.selectAll('.candle')
      .data(data)
      .enter()
      .append('g')
      .attr('class', 'candle')
      .attr('transform', (d, i) => `translate(${xScale(i)},0)`);

    // الذيول
    candles.append('line')
      .attr('class', 'wick')
      .attr('x1', 0)
      .attr('x2', 0)
      .attr('y1', d => yScale(d.high))
      .attr('y2', d => yScale(d.low))
      .style('stroke', d => d.close > d.open ? colors.bullish : colors.bearish)
      .style('stroke-width', 1);

    // الأجسام
    candles.append('rect')
      .attr('class', 'body')
      .attr('x', -candleWidth / 2)
      .attr('y', d => yScale(Math.max(d.open, d.close)))
      .attr('width', candleWidth)
      .attr('height', d => Math.abs(yScale(d.open) - yScale(d.close)) || 1)
      .style('fill', d => d.close > d.open ? colors.bullish : colors.bearish)
      .style('stroke', d => d.close > d.open ? colors.bullish : colors.bearish)
      .style('stroke-width', 1);

    // التفاعل مع الشموع
    candles.append('rect')
      .attr('class', 'interaction')
      .attr('x', -candleWidth / 2)
      .attr('y', 0)
      .attr('width', candleWidth)
      .attr('height', priceHeight)
      .style('fill', 'transparent')
      .style('cursor', 'crosshair')
      .on('mouseover', function(event, d) {
        // إظهار التولتيب
        const tooltip = d3.select('body').append('div')
          .attr('class', 'tooltip')
          .style('position', 'absolute')
          .style('background', colors.background)
          .style('border', `1px solid ${colors.grid}`)
          .style('border-radius', '4px')
          .style('padding', '10px')
          .style('color', colors.text)
          .style('font-size', '12px')
          .style('pointer-events', 'none')
          .style('opacity', 0);

        tooltip.html(`
          <div>الوقت: ${new Date(d.timestamp).toLocaleString('ar-SA')}</div>
          <div>فتح: ${d.open.toFixed(5)}</div>
          <div>أعلى: ${d.high.toFixed(5)}</div>
          <div>أقل: ${d.low.toFixed(5)}</div>
          <div>إغلاق: ${d.close.toFixed(5)}</div>
          <div>الحجم: ${(d.volume / 1000000).toFixed(2)}M</div>
        `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px')
        .transition()
        .duration(200)
        .style('opacity', 1);

        // تمييز الشمعة
        d3.select(this.parentNode).select('.body')
          .style('stroke-width', 3);
      })
      .on('mouseout', function() {
        // إخفاء التولتيب
        d3.selectAll('.tooltip').remove();
        
        // إزالة التمييز
        d3.select(this.parentNode).select('.body')
          .style('stroke-width', 1);
      });

    // رسم الحجم
    if (showVolume) {
      const volumeG = g.append('g')
        .attr('transform', `translate(0,${priceHeight + 20})`);

      volumeG.selectAll('.volume-bar')
        .data(data)
        .enter()
        .append('rect')
        .attr('class', 'volume-bar')
        .attr('x', (d, i) => xScale(i) - candleWidth / 2)
        .attr('y', d => volumeScale(d.volume))
        .attr('width', candleWidth)
        .attr('height', d => volumeHeight - volumeScale(d.volume))
        .style('fill', d => d.close > d.open ? colors.bullish : colors.bearish)
        .style('opacity', 0.6);

      // محور الحجم
      const volumeAxis = d3.axisRight(volumeScale)
        .tickFormat(d => `${(d as number / 1000000).toFixed(1)}M`);

      volumeG.append('g')
        .attr('transform', `translate(${chartWidth},0)`)
        .call(volumeAxis)
        .selectAll('text')
        .style('fill', colors.text)
        .style('font-size', '10px');
    }

    // العنوان
    svg.append('text')
      .attr('x', width / 2)
      .attr('y', 30)
      .attr('text-anchor', 'middle')
      .style('fill', colors.text)
      .style('font-size', '18px')
      .style('font-weight', 'bold')
      .text(`${symbol} - ${timeframe}`);

    // تسميات المحاور
    svg.append('text')
      .attr('x', width - 25)
      .attr('y', margin.top + priceHeight / 2)
      .attr('text-anchor', 'middle')
      .style('fill', colors.text)
      .style('font-size', '12px')
      .text('السعر');

    if (showVolume) {
      svg.append('text')
        .attr('x', width - 25)
        .attr('y', margin.top + priceHeight + 60)
        .attr('text-anchor', 'middle')
        .style('fill', colors.text)
        .style('font-size', '12px')
        .text('الحجم');
    }
  };

  return (
    <div className="relative">
      <svg ref={svgRef} className="border border-slate-600 rounded-lg"></svg>
      
      {/* معلومات إضافية */}
      <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>صعود</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>هبوط</span>
          </div>
          <div className="text-gray-400">
            D3.js • الشموع: {data.length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default D3CandlestickChart;
