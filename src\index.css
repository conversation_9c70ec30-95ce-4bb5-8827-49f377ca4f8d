
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force dark theme immediately */
html {
  background-color: #0f172a !important;
  color: #f8fafc !important;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #0f172a !important;
  color: #f8fafc !important;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

#root {
  min-height: 100vh;
  width: 100%;
  background-color: #0f172a !important;
  color: #f8fafc !important;
}

/* Ensure all text is visible */
* {
  color: inherit;
}

.text-white {
  color: #ffffff !important;
}

.text-gray-300 {
  color: #d1d5db !important;
}

.text-gray-400 {
  color: #9ca3af !important;
}

/* Fix any potential dark mode issues */
div, span, p, h1, h2, h3, h4, h5, h6 {
  color: inherit;
}
