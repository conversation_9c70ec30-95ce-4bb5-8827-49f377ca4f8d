import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Html, Stars } from '@react-three/drei';

const mockAssets = [
  { symbol: 'BTCUSD', name: 'بيتكوين', price: 65000, change: 2.3 },
  { symbol: 'ETHUSD', name: 'إيثيريوم', price: 3200, change: -1.1 },
  { symbol: 'AAPL', name: 'Apple', price: 190, change: 0.7 },
  { symbol: 'EURUSD', name: 'يورو/دولار', price: 1.09, change: 0.2 },
  { symbol: 'TSLA', name: 'تسلا', price: 720, change: -3.2 },
];

function AssetSphere({ asset, position }: any) {
  const mesh = useRef<any>();
  useFrame(() => {
    if (mesh.current) {
      mesh.current.rotation.y += 0.003;
    }
  });
  return (
    <mesh ref={mesh} position={position}>
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial color={asset.change > 0 ? '#00FF88' : '#FF4444'} />
      <Html center>
        <div style={{ textAlign: 'center', color: 'white', fontWeight: 'bold', fontSize: 16 }}>
          {asset.name}<br />
          <span style={{ color: asset.change > 0 ? '#00FF88' : '#FF4444' }}>{asset.price}</span>
        </div>
      </Html>
    </mesh>
  );
}

export default function Market3DView() {
  return (
    <div style={{ height: 400, borderRadius: 16, overflow: 'hidden', background: '#181f2a', margin: '24px 0' }}>
      <Canvas camera={{ position: [0, 0, 10], fov: 60 }} shadows>
        <ambientLight intensity={0.7} />
        <pointLight position={[10, 10, 10]} intensity={1.2} />
        <Stars radius={30} depth={60} count={2000} factor={7} fade />
        {mockAssets.map((asset, i) => (
          <AssetSphere key={asset.symbol} asset={asset} position={[Math.cos(i * 1.2) * 4, Math.sin(i * 1.2) * 2, (i - 2) * 1.5]} />
        ))}
        <OrbitControls enablePan enableZoom enableRotate />
      </Canvas>
      <div style={{ position: 'absolute', top: 16, right: 24, color: '#fff', fontWeight: 'bold', fontSize: 18, zIndex: 2 }}>
        سوق ثلاثي الأبعاد (تجريبي)
      </div>
    </div>
  );
} 