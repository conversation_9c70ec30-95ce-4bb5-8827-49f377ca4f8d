import React, { useState, useEffect } from 'react';
import { Search, Download, FileText, BarChart3, TrendingUp, AlertTriangle, Target, Eye, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { enhancedMarketDataService, MarketData } from '@/services/enhancedMarketDataService';
import { patternDetectionService, DetectedPattern, PatternStatistics } from '@/services/patternDetectionService';
import { exportService, ExportOptions } from '@/services/exportService';
import CanvasCandlestickChart from '@/components/charts/CanvasCandlestickChart';

interface PatternDetectionAndExportProps {
  lang?: 'en' | 'ar';
}

const PatternDetectionAndExport: React.FC<PatternDetectionAndExportProps> = ({ lang = 'ar' }) => {
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [detectedPatterns, setDetectedPatterns] = useState<DetectedPattern[]>([]);
  const [patternStats, setPatternStats] = useState<PatternStatistics | null>(null);
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [selectedTimeframe, setSelectedTimeframe] = useState('1h');
  const [isDetecting, setIsDetecting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedPattern, setSelectedPattern] = useState<DetectedPattern | null>(null);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includePatterns: true,
    includeIndicators: true,
    includeStatistics: true,
    includeChartData: true,
    language: 'ar'
  });

  const symbols = enhancedMarketDataService.getAvailableSymbols();
  const timeframes = enhancedMarketDataService.getAvailableTimeframes();

  useEffect(() => {
    loadMarketData();
  }, [selectedSymbol, selectedTimeframe]);

  const loadMarketData = async () => {
    try {
      console.log(`Loading market data for ${selectedSymbol} ${selectedTimeframe}`);
      const data = await enhancedMarketDataService.getMarketData(selectedSymbol, selectedTimeframe);
      setMarketData(data);
    } catch (error) {
      console.error('Error loading market data:', error);
    }
  };

  const detectPatterns = async () => {
    if (!marketData) return;

    setIsDetecting(true);
    try {
      console.log('🔍 Starting pattern detection...');
      const patterns = await patternDetectionService.detectAllPatterns(marketData.candlesticks);
      setDetectedPatterns(patterns);
      
      const stats = patternDetectionService.getPatternStatistics();
      setPatternStats(stats);
      
      console.log(`✅ Detected ${patterns.length} patterns`);
    } catch (error) {
      console.error('Error detecting patterns:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  const handleExport = async (format: 'csv' | 'json') => {
    if (!marketData) return;

    setIsExporting(true);
    try {
      const options = { ...exportOptions, format };
      
      if (format === 'csv') {
        await exportService.quickExportCSV(marketData, detectedPatterns, selectedSymbol);
      } else {
        await exportService.quickExportJSON(marketData, detectedPatterns, selectedSymbol);
      }
      
      console.log(`✅ Exported data in ${format} format`);
    } catch (error) {
      console.error('Error exporting data:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getPatternTypeColor = (type: string) => {
    switch (type) {
      case 'bullish': return 'text-green-400 bg-green-900/20 border-green-400';
      case 'bearish': return 'text-red-400 bg-red-900/20 border-red-400';
      case 'neutral': return 'text-yellow-400 bg-yellow-900/20 border-yellow-400';
      case 'reversal': return 'text-purple-400 bg-purple-900/20 border-purple-400';
      case 'continuation': return 'text-blue-400 bg-blue-900/20 border-blue-400';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-400';
    }
  };

  const getSignificanceIcon = (significance: string) => {
    switch (significance) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-400" />;
      case 'high': return <TrendingUp className="h-4 w-4 text-orange-400" />;
      case 'medium': return <Target className="h-4 w-4 text-yellow-400" />;
      case 'low': return <Eye className="h-4 w-4 text-gray-400" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-400" />;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-400';
    if (confidence >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* العنوان الرئيسي */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <Search className="h-8 w-8 text-blue-400" />
            {lang === 'ar' ? 'كشف الأنماط وتصدير التقارير' : 'Pattern Detection & Export Reports'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar' 
              ? 'كشف تلقائي للأنماط الفنية مع تصدير التقارير المتقدمة بصيغ CSV و JSON'
              : 'Automatic technical pattern detection with advanced CSV and JSON report export'
            }
          </p>
        </div>

        {/* شريط التحكم */}
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-center">
              {/* اختيار الرمز */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'الرمز' : 'Symbol'}
                </label>
                <Select value={selectedSymbol} onValueChange={setSelectedSymbol}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {symbols.map(symbol => (
                      <SelectItem key={symbol} value={symbol}>{symbol}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* اختيار الإطار الزمني */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'الإطار الزمني' : 'Timeframe'}
                </label>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {timeframes.map(tf => (
                      <SelectItem key={tf} value={tf}>{tf}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* زر كشف الأنماط */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'كشف الأنماط' : 'Pattern Detection'}
                </label>
                <Button
                  onClick={detectPatterns}
                  disabled={isDetecting || !marketData}
                  className="w-full flex items-center gap-2"
                >
                  <Search className={`h-4 w-4 ${isDetecting ? 'animate-spin' : ''}`} />
                  {isDetecting 
                    ? (lang === 'ar' ? 'جاري الكشف...' : 'Detecting...') 
                    : (lang === 'ar' ? 'كشف الأنماط' : 'Detect Patterns')
                  }
                </Button>
              </div>

              {/* أزرار التصدير */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'تصدير CSV' : 'Export CSV'}
                </label>
                <Button
                  onClick={() => handleExport('csv')}
                  disabled={isExporting || detectedPatterns.length === 0}
                  variant="outline"
                  className="w-full flex items-center gap-2"
                >
                  <FileText className="h-4 w-4" />
                  CSV
                </Button>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'تصدير JSON' : 'Export JSON'}
                </label>
                <Button
                  onClick={() => handleExport('json')}
                  disabled={isExporting || detectedPatterns.length === 0}
                  variant="outline"
                  className="w-full flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  JSON
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات سريعة */}
        {patternStats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <Search className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'إجمالي الأنماط' : 'Total Patterns'}
                </h3>
                <p className="text-2xl font-bold text-blue-400">
                  {patternStats.totalPatterns}
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'أنماط صعودية' : 'Bullish Patterns'}
                </h3>
                <p className="text-2xl font-bold text-green-400">
                  {patternStats.bullishPatterns}
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <BarChart3 className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'أنماط هبوطية' : 'Bearish Patterns'}
                </h3>
                <p className="text-2xl font-bold text-red-400">
                  {patternStats.bearishPatterns}
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <Target className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'متوسط الثقة' : 'Avg Confidence'}
                </h3>
                <p className="text-2xl font-bold text-purple-400">
                  {patternStats.averageConfidence}%
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <AlertTriangle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'عالية الثقة' : 'High Confidence'}
                </h3>
                <p className="text-2xl font-bold text-yellow-400">
                  {patternStats.highConfidencePatterns}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* التبويبات الرئيسية */}
        <Tabs defaultValue="chart" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800">
            <TabsTrigger value="chart" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الرسم البياني' : 'Chart'}
            </TabsTrigger>
            <TabsTrigger value="patterns" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الأنماط' : 'Patterns'}
            </TabsTrigger>
            <TabsTrigger value="statistics" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الإحصائيات' : 'Statistics'}
            </TabsTrigger>
            <TabsTrigger value="export" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'التصدير' : 'Export'}
            </TabsTrigger>
          </TabsList>

          {/* تبويب الرسم البياني */}
          <TabsContent value="chart" className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-400" />
                    {selectedSymbol} - {selectedTimeframe}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">
                      {lang === 'ar' ? 'أنماط مكتشفة:' : 'Patterns:'} {detectedPatterns.length}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {marketData ? (
                  <div className="w-full overflow-x-auto">
                    <CanvasCandlestickChart
                      data={marketData.candlesticks}
                      indicators={marketData.indicators}
                      supportLevels={marketData.supportLevels}
                      resistanceLevels={marketData.resistanceLevels}
                      patterns={detectedPatterns}
                      symbol={selectedSymbol}
                      timeframe={selectedTimeframe}
                      height={600}
                      width={1200}
                      showVolume={true}
                      theme="dark"
                      onPatternClick={setSelectedPattern}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-96">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-400">
                        {lang === 'ar' ? 'جاري تحميل البيانات...' : 'Loading data...'}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب الأنماط */}
          <TabsContent value="patterns" className="space-y-6">
            {detectedPatterns.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {detectedPatterns.map((pattern, index) => (
                  <Card
                    key={pattern.id}
                    className={`bg-slate-800 border-slate-700 cursor-pointer transition-colors ${
                      selectedPattern?.id === pattern.id ? 'border-blue-400 bg-blue-900/20' : 'hover:border-slate-600'
                    }`}
                    onClick={() => setSelectedPattern(pattern)}
                  >
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getSignificanceIcon(pattern.significance)}
                          <span className="text-lg">
                            {lang === 'ar' ? pattern.nameAr : pattern.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getPatternTypeColor(pattern.type)}>
                            {pattern.type}
                          </Badge>
                          <Badge variant="outline">
                            #{index + 1}
                          </Badge>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* الثقة والاحتمالية */}
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm text-gray-400 mb-1">
                              {lang === 'ar' ? 'الثقة:' : 'Confidence:'}
                            </div>
                            <div className={`text-lg font-bold ${getConfidenceColor(pattern.confidence)}`}>
                              {pattern.confidence}%
                            </div>
                            <Progress value={pattern.confidence} className="h-2 mt-1" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-400 mb-1">
                              {lang === 'ar' ? 'الاحتمالية:' : 'Probability:'}
                            </div>
                            <div className="text-lg font-bold text-blue-400">
                              {pattern.probability}%
                            </div>
                            <Progress value={pattern.probability} className="h-2 mt-1" />
                          </div>
                        </div>

                        {/* الوصف */}
                        <div>
                          <div className="text-sm text-gray-400 mb-1">
                            {lang === 'ar' ? 'الوصف:' : 'Description:'}
                          </div>
                          <p className="text-sm text-gray-300">
                            {lang === 'ar' ? pattern.descriptionAr : pattern.description}
                          </p>
                        </div>

                        {/* التوقيت */}
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm text-gray-400 mb-1">
                              {lang === 'ar' ? 'البداية:' : 'Start:'}
                            </div>
                            <div className="text-sm text-white">
                              {new Date(pattern.startTime).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                            </div>
                          </div>
                          <div>
                            <div className="text-sm text-gray-400 mb-1">
                              {lang === 'ar' ? 'النهاية:' : 'End:'}
                            </div>
                            <div className="text-sm text-white">
                              {new Date(pattern.endTime).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                            </div>
                          </div>
                        </div>

                        {/* الأهداف */}
                        {pattern.priceTarget && pattern.stopLoss && (
                          <div className="grid grid-cols-2 gap-4 pt-2 border-t border-slate-700">
                            <div>
                              <div className="text-sm text-gray-400 mb-1">
                                {lang === 'ar' ? 'الهدف:' : 'Target:'}
                              </div>
                              <div className="text-sm font-bold text-green-400">
                                {pattern.priceTarget.toFixed(5)}
                              </div>
                            </div>
                            <div>
                              <div className="text-sm text-gray-400 mb-1">
                                {lang === 'ar' ? 'وقف الخسارة:' : 'Stop Loss:'}
                              </div>
                              <div className="text-sm font-bold text-red-400">
                                {pattern.stopLoss.toFixed(5)}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* معلومات إضافية */}
                        <div className="flex justify-between items-center pt-2 border-t border-slate-700">
                          <Badge variant="outline" className="text-xs">
                            {pattern.significance}
                          </Badge>
                          <div className="text-xs text-gray-400">
                            {pattern.patternData.trendContext}
                          </div>
                          {pattern.patternData.volumeConfirmation && (
                            <Badge variant="outline" className="text-xs text-green-400">
                              {lang === 'ar' ? 'تأكيد الحجم' : 'Volume Confirmed'}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-12 text-center">
                  <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {lang === 'ar' ? 'لا توجد أنماط مكتشفة' : 'No Patterns Detected'}
                  </h3>
                  <p className="text-gray-400 mb-4">
                    {lang === 'ar' ? 'انقر على "كشف الأنماط" لبدء التحليل' : 'Click "Detect Patterns" to start analysis'}
                  </p>
                  <Button onClick={detectPatterns} disabled={isDetecting || !marketData}>
                    <Search className="h-4 w-4 mr-2" />
                    {lang === 'ar' ? 'كشف الأنماط' : 'Detect Patterns'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* تبويب الإحصائيات */}
          <TabsContent value="statistics" className="space-y-6">
            {patternStats ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* إحصائيات عامة */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-blue-400" />
                      {lang === 'ar' ? 'إحصائيات عامة' : 'General Statistics'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'إجمالي الأنماط:' : 'Total Patterns:'}</span>
                        <span className="text-white font-bold">{patternStats.totalPatterns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'أنماط صعودية:' : 'Bullish Patterns:'}</span>
                        <span className="text-green-400 font-bold">{patternStats.bullishPatterns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'أنماط هبوطية:' : 'Bearish Patterns:'}</span>
                        <span className="text-red-400 font-bold">{patternStats.bearishPatterns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'أنماط محايدة:' : 'Neutral Patterns:'}</span>
                        <span className="text-yellow-400 font-bold">{patternStats.neutralPatterns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'عالية الثقة:' : 'High Confidence:'}</span>
                        <span className="text-purple-400 font-bold">{patternStats.highConfidencePatterns}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'متوسط الثقة:' : 'Average Confidence:'}</span>
                        <span className="text-blue-400 font-bold">{patternStats.averageConfidence}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'معدل النجاح:' : 'Success Rate:'}</span>
                        <span className="text-green-400 font-bold">{patternStats.successRate}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* الأنماط حسب النوع */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-green-400" />
                      {lang === 'ar' ? 'الأنماط حسب النوع' : 'Patterns by Type'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {Object.entries(patternStats.patternsByType).map(([pattern, count]) => (
                        <div key={pattern} className="flex justify-between items-center">
                          <span className="text-gray-400">{pattern}:</span>
                          <div className="flex items-center gap-2">
                            <span className="text-white font-bold">{count}</span>
                            <div className="w-20 bg-slate-700 rounded-full h-2">
                              <div
                                className="bg-blue-400 h-2 rounded-full"
                                style={{ width: `${(count / patternStats.totalPatterns) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* أفضل الأنماط */}
                <Card className="bg-slate-800 border-slate-700 lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-purple-400" />
                      {lang === 'ar' ? 'أفضل الأنماط (أعلى ثقة)' : 'Top Patterns (Highest Confidence)'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {detectedPatterns.slice(0, 5).map((pattern, index) => (
                        <div key={pattern.id} className="flex items-center justify-between p-3 bg-slate-700 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-lg font-bold text-blue-400">#{index + 1}</div>
                            <div>
                              <div className="font-medium text-white">
                                {lang === 'ar' ? pattern.nameAr : pattern.name}
                              </div>
                              <div className="text-sm text-gray-400">
                                {new Date(pattern.startTime).toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <Badge className={getPatternTypeColor(pattern.type)}>
                              {pattern.type}
                            </Badge>
                            <div className="text-right">
                              <div className={`font-bold ${getConfidenceColor(pattern.confidence)}`}>
                                {pattern.confidence}%
                              </div>
                              <div className="text-xs text-gray-400">
                                {pattern.significance}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-12 text-center">
                  <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {lang === 'ar' ? 'لا توجد إحصائيات' : 'No Statistics Available'}
                  </h3>
                  <p className="text-gray-400">
                    {lang === 'ar' ? 'قم بكشف الأنماط أولاً لرؤية الإحصائيات' : 'Detect patterns first to see statistics'}
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* تبويب التصدير */}
          <TabsContent value="export" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* خيارات التصدير */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-gray-400" />
                    {lang === 'ar' ? 'خيارات التصدير' : 'Export Options'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'تضمين الأنماط' : 'Include Patterns'}
                    </label>
                    <Switch
                      checked={exportOptions.includePatterns}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includePatterns: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'تضمين المؤشرات' : 'Include Indicators'}
                    </label>
                    <Switch
                      checked={exportOptions.includeIndicators}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeIndicators: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'تضمين الإحصائيات' : 'Include Statistics'}
                    </label>
                    <Switch
                      checked={exportOptions.includeStatistics}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeStatistics: checked }))
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'تضمين بيانات الرسم البياني' : 'Include Chart Data'}
                    </label>
                    <Switch
                      checked={exportOptions.includeChartData}
                      onCheckedChange={(checked) =>
                        setExportOptions(prev => ({ ...prev, includeChartData: checked }))
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'اللغة' : 'Language'}
                    </label>
                    <Select
                      value={exportOptions.language}
                      onValueChange={(value: 'ar' | 'en') =>
                        setExportOptions(prev => ({ ...prev, language: value }))
                      }
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="ar">العربية</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار التصدير */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5 text-blue-400" />
                    {lang === 'ar' ? 'تصدير التقارير' : 'Export Reports'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={() => handleExport('csv')}
                    disabled={isExporting || detectedPatterns.length === 0}
                    className="w-full flex items-center gap-2"
                    size="lg"
                  >
                    <FileText className="h-5 w-5" />
                    {isExporting
                      ? (lang === 'ar' ? 'جاري التصدير...' : 'Exporting...')
                      : (lang === 'ar' ? 'تصدير CSV' : 'Export CSV')
                    }
                  </Button>

                  <Button
                    onClick={() => handleExport('json')}
                    disabled={isExporting || detectedPatterns.length === 0}
                    variant="outline"
                    className="w-full flex items-center gap-2"
                    size="lg"
                  >
                    <Download className="h-5 w-5" />
                    {isExporting
                      ? (lang === 'ar' ? 'جاري التصدير...' : 'Exporting...')
                      : (lang === 'ar' ? 'تصدير JSON' : 'Export JSON')
                    }
                  </Button>

                  <div className="mt-6 p-4 bg-slate-700 rounded-lg">
                    <h4 className="font-medium text-white mb-2">
                      {lang === 'ar' ? 'معلومات التصدير:' : 'Export Info:'}
                    </h4>
                    <div className="text-sm text-gray-400 space-y-1">
                      <div>
                        {lang === 'ar' ? 'الرمز:' : 'Symbol:'} {selectedSymbol}
                      </div>
                      <div>
                        {lang === 'ar' ? 'الإطار الزمني:' : 'Timeframe:'} {selectedTimeframe}
                      </div>
                      <div>
                        {lang === 'ar' ? 'الأنماط:' : 'Patterns:'} {detectedPatterns.length}
                      </div>
                      <div>
                        {lang === 'ar' ? 'نقاط البيانات:' : 'Data Points:'} {marketData?.candlesticks.length || 0}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PatternDetectionAndExport;
