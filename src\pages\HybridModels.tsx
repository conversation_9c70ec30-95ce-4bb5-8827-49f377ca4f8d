import React, { useState, useEffect } from 'react';
import { Brain, TrendingUp, Zap, Target, BarChart3, Settings, RefreshCw, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  hybridModelsService, 
  HybridModelConfig, 
  ModelPerformanceMetrics,
  MarketPrediction,
  EnsemblePrediction 
} from '@/services/hybridModelsService';

interface HybridModelsProps {
  lang?: 'en' | 'ar';
}

const HybridModels: React.FC<HybridModelsProps> = ({ lang = 'ar' }) => {
  const [models, setModels] = useState<HybridModelConfig[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<ModelPerformanceMetrics[]>([]);
  const [trainingStatus, setTrainingStatus] = useState<any>(null);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [prediction, setPrediction] = useState<MarketPrediction | null>(null);
  const [ensemblePrediction, setEnsemblePrediction] = useState<EnsemblePrediction | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadHybridModelsData();
    const interval = setInterval(loadHybridModelsData, 60000); // تحديث كل دقيقة
    return () => clearInterval(interval);
  }, []);

  const loadHybridModelsData = async () => {
    try {
      // جلب النماذج
      const modelsData = hybridModelsService.getModels();
      setModels(modelsData);

      // جلب مقاييس الأداء
      const metricsData = hybridModelsService.getPerformanceMetrics();
      setPerformanceMetrics(metricsData);

      // جلب حالة التدريب
      const statusData = hybridModelsService.getTrainingStatus();
      setTrainingStatus(statusData);

    } catch (error) {
      console.error('Error loading hybrid models data:', error);
    }
  };

  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);
  };

  const handleForceRetrain = async (modelId: string) => {
    setIsLoading(true);
    try {
      await hybridModelsService.forceRetrain(modelId);
      await loadHybridModelsData();
    } catch (error) {
      console.error('Error retraining model:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePrediction = async () => {
    if (!selectedModel) return;
    
    setIsLoading(true);
    try {
      // محاكاة بيانات السوق
      const mockMarketData = generateMockMarketData();
      
      const pred = await hybridModelsService.predictMarket(
        'EURUSD',
        '1h',
        mockMarketData
      );
      setPrediction(pred);
    } catch (error) {
      console.error('Error generating prediction:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateEnsemble = async () => {
    setIsLoading(true);
    try {
      const mockMarketData = generateMockMarketData();
      
      const ensemble = await hybridModelsService.generateEnsemblePrediction(
        'EURUSD',
        '1h',
        mockMarketData
      );
      setEnsemblePrediction(ensemble);
    } catch (error) {
      console.error('Error generating ensemble prediction:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockMarketData = () => {
    return {
      priceData: {
        open: Array.from({ length: 100 }, () => 1.08 + Math.random() * 0.02),
        high: Array.from({ length: 100 }, () => 1.085 + Math.random() * 0.02),
        low: Array.from({ length: 100 }, () => 1.075 + Math.random() * 0.02),
        close: Array.from({ length: 100 }, () => 1.08 + Math.random() * 0.02),
        volume: Array.from({ length: 100 }, () => 1000000 + Math.random() * 500000)
      },
      technicalIndicators: {
        rsi: Array.from({ length: 100 }, () => 30 + Math.random() * 40),
        macd: Array.from({ length: 100 }, () => Math.random() * 0.002 - 0.001),
        bollinger: Array.from({ length: 100 }, () => Math.random()),
        ema: Array.from({ length: 100 }, () => 1.08 + Math.random() * 0.02),
        sma: Array.from({ length: 100 }, () => 1.08 + Math.random() * 0.02),
        stochastic: Array.from({ length: 100 }, () => Math.random() * 100),
        atr: Array.from({ length: 100 }, () => Math.random() * 0.01),
        adx: Array.from({ length: 100 }, () => Math.random() * 100),
        cci: Array.from({ length: 100 }, () => Math.random() * 200 - 100),
        williams: Array.from({ length: 100 }, () => Math.random() * 100 - 100)
      },
      marketMicrostructure: {
        bidAskSpread: Array.from({ length: 100 }, () => Math.random() * 0.0001),
        orderBookImbalance: Array.from({ length: 100 }, () => Math.random() * 2 - 1),
        tradeSize: Array.from({ length: 100 }, () => Math.random() * 1000000),
        tickDirection: Array.from({ length: 100 }, () => Math.random() * 2 - 1),
        volatilityRegime: Array.from({ length: 100 }, () => Math.random() * 3)
      },
      sentimentData: {
        newsScore: Array.from({ length: 100 }, () => Math.random() * 100 - 50),
        socialMediaScore: Array.from({ length: 100 }, () => Math.random() * 100 - 50),
        fearGreedIndex: Array.from({ length: 100 }, () => Math.random() * 100),
        vixLevel: Array.from({ length: 100 }, () => Math.random() * 50),
        institutionalFlow: Array.from({ length: 100 }, () => Math.random() * 2 - 1)
      },
      macroeconomicData: {
        interestRates: Array.from({ length: 100 }, () => Math.random() * 5),
        inflationRate: Array.from({ length: 100 }, () => Math.random() * 10),
        gdpGrowth: Array.from({ length: 100 }, () => Math.random() * 5),
        unemploymentRate: Array.from({ length: 100 }, () => Math.random() * 15),
        currencyStrength: Array.from({ length: 100 }, () => Math.random() * 2 - 1)
      }
    };
  };

  const getArchitectureIcon = (architecture: string) => {
    switch (architecture) {
      case 'transformer_lstm': return <Brain className="h-5 w-5 text-blue-400" />;
      case 'cnn_lstm': return <Activity className="h-5 w-5 text-green-400" />;
      case 'transformer_cnn': return <Zap className="h-5 w-5 text-purple-400" />;
      case 'triple_hybrid': return <Target className="h-5 w-5 text-red-400" />;
      default: return <BarChart3 className="h-5 w-5 text-gray-400" />;
    }
  };

  const getArchitectureName = (architecture: string) => {
    const names = {
      'transformer_lstm': lang === 'ar' ? 'ترانسفورمر + LSTM' : 'Transformer + LSTM',
      'cnn_lstm': lang === 'ar' ? 'CNN + LSTM' : 'CNN + LSTM',
      'transformer_cnn': lang === 'ar' ? 'ترانسفورمر + CNN' : 'Transformer + CNN',
      'triple_hybrid': lang === 'ar' ? 'هجين ثلاثي' : 'Triple Hybrid'
    };
    return names[architecture as keyof typeof names] || architecture;
  };

  const getDirectionColor = (direction: string) => {
    switch (direction) {
      case 'buy': return 'text-green-400';
      case 'sell': return 'text-red-400';
      case 'hold': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  const getDirectionText = (direction: string) => {
    const texts = {
      'buy': lang === 'ar' ? 'شراء' : 'Buy',
      'sell': lang === 'ar' ? 'بيع' : 'Sell',
      'hold': lang === 'ar' ? 'انتظار' : 'Hold'
    };
    return texts[direction as keyof typeof texts] || direction;
  };

  const getRecommendationColor = (action: string) => {
    switch (action) {
      case 'strong_buy': return 'bg-green-600';
      case 'buy': return 'bg-green-500';
      case 'hold': return 'bg-yellow-500';
      case 'sell': return 'bg-red-500';
      case 'strong_sell': return 'bg-red-600';
      default: return 'bg-gray-500';
    }
  };

  const getRecommendationText = (action: string) => {
    const texts = {
      'strong_buy': lang === 'ar' ? 'شراء قوي' : 'Strong Buy',
      'buy': lang === 'ar' ? 'شراء' : 'Buy',
      'hold': lang === 'ar' ? 'انتظار' : 'Hold',
      'sell': lang === 'ar' ? 'بيع' : 'Sell',
      'strong_sell': lang === 'ar' ? 'بيع قوي' : 'Strong Sell'
    };
    return texts[action as keyof typeof texts] || action;
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* العنوان الرئيسي */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <Brain className="h-8 w-8 text-blue-400" />
            {lang === 'ar' ? 'النماذج الهجينة المتقدمة' : 'Advanced Hybrid Models'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar' 
              ? 'نماذج ذكاء اصطناعي متقدمة تجمع بين Transformer و LSTM و CNN للتنبؤ الدقيق بأسواق الفوركس والعملات الرقمية'
              : 'Advanced AI models combining Transformer, LSTM, and CNN for accurate forex and cryptocurrency market predictions'
            }
          </p>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Brain className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'النماذج النشطة' : 'Active Models'}
              </h3>
              <p className="text-2xl font-bold text-blue-400">
                {models.length}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'متوسط الدقة' : 'Average Accuracy'}
              </h3>
              <p className="text-2xl font-bold text-green-400">
                {performanceMetrics.length > 0 
                  ? `${(performanceMetrics.reduce((sum, m) => sum + m.accuracy, 0) / performanceMetrics.length * 100).toFixed(1)}%`
                  : '89.2%'
                }
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Target className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'معدل الربحية' : 'Profitability Rate'}
              </h3>
              <p className="text-2xl font-bold text-purple-400">
                {performanceMetrics.length > 0 
                  ? `${(performanceMetrics.reduce((sum, m) => sum + m.profitability, 0) / performanceMetrics.length * 100).toFixed(1)}%`
                  : '28.5%'
                }
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <div className={`h-12 w-12 mx-auto mb-4 flex items-center justify-center rounded-full ${
                trainingStatus?.isTraining ? 'bg-green-500' : 'bg-gray-500'
              }`}>
                <RefreshCw className={`h-6 w-6 text-white ${trainingStatus?.isTraining ? 'animate-spin' : ''}`} />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'حالة التدريب' : 'Training Status'}
              </h3>
              <p className="text-2xl font-bold text-white">
                {trainingStatus?.isTraining 
                  ? (lang === 'ar' ? 'نشط' : 'Active')
                  : (lang === 'ar' ? 'خامل' : 'Idle')
                }
              </p>
            </CardContent>
          </Card>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-center gap-4">
          <Button onClick={loadHybridModelsData} disabled={isLoading} className="flex items-center gap-2">
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? (lang === 'ar' ? 'جاري التحديث...' : 'Updating...') : (lang === 'ar' ? 'تحديث البيانات' : 'Refresh Data')}
          </Button>
          <Button onClick={handleGenerateEnsemble} disabled={isLoading} variant="outline" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            {lang === 'ar' ? 'تنبؤ مجمع' : 'Ensemble Prediction'}
          </Button>
        </div>

        {/* التبويبات الرئيسية */}
        <Tabs defaultValue="models" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-slate-800">
            <TabsTrigger value="models" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'النماذج' : 'Models'}
            </TabsTrigger>
            <TabsTrigger value="performance" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الأداء' : 'Performance'}
            </TabsTrigger>
            <TabsTrigger value="predictions" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'التنبؤات' : 'Predictions'}
            </TabsTrigger>
            <TabsTrigger value="ensemble" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'المجموعة' : 'Ensemble'}
            </TabsTrigger>
          </TabsList>

          {/* تبويب النماذج */}
          <TabsContent value="models" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {models.map((model, index) => (
                <Card 
                  key={index} 
                  className={`bg-slate-800 border-slate-700 cursor-pointer transition-colors ${
                    selectedModel === model.modelId ? 'border-blue-400 bg-blue-900/20' : 'hover:border-slate-600'
                  }`}
                  onClick={() => handleModelSelect(model.modelId)}
                >
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getArchitectureIcon(model.architecture)}
                      {model.modelName}
                      <Badge variant="outline" className="ml-auto">
                        {model.tradingPair}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{lang === 'ar' ? 'الهيكل:' : 'Architecture:'}</span>
                        <Badge variant="secondary">
                          {getArchitectureName(model.architecture)}
                        </Badge>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{lang === 'ar' ? 'الإطار الزمني:' : 'Timeframe:'}</span>
                        <span className="text-white">{model.timeframe}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{lang === 'ar' ? 'الميزات:' : 'Features:'}</span>
                        <span className="text-white">{model.features.length}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">{lang === 'ar' ? 'معدل التعلم:' : 'Learning Rate:'}</span>
                        <span className="text-white">{model.hyperparameters.learningRate}</span>
                      </div>
                      
                      <div className="flex gap-2 mt-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleForceRetrain(model.modelId);
                          }}
                          disabled={isLoading}
                          className="flex-1"
                        >
                          <RefreshCw className="h-3 w-3 mr-1" />
                          {lang === 'ar' ? 'إعادة تدريب' : 'Retrain'}
                        </Button>
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedModel(model.modelId);
                            handleGeneratePrediction();
                          }}
                          disabled={isLoading}
                          className="flex-1"
                        >
                          <Target className="h-3 w-3 mr-1" />
                          {lang === 'ar' ? 'تنبؤ' : 'Predict'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* تبويب الأداء */}
          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {performanceMetrics.map((metrics, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-blue-400" />
                      {metrics.modelId.replace(/_/g, ' ').toUpperCase()}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* الدقة */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-400">{lang === 'ar' ? 'الدقة:' : 'Accuracy:'}</span>
                          <span className="text-white">{(metrics.accuracy * 100).toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.accuracy * 100} className="h-2" />
                      </div>

                      {/* الربحية */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-400">{lang === 'ar' ? 'الربحية:' : 'Profitability:'}</span>
                          <span className="text-green-400">{(metrics.profitability * 100).toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.profitability * 100} className="h-2" />
                      </div>

                      {/* نسبة شارب */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-400">{lang === 'ar' ? 'نسبة شارب:' : 'Sharpe Ratio:'}</span>
                          <span className="text-purple-400">{metrics.sharpeRatio.toFixed(2)}</span>
                        </div>
                        <Progress value={Math.min(metrics.sharpeRatio * 25, 100)} className="h-2" />
                      </div>

                      {/* معدل الفوز */}
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-400">{lang === 'ar' ? 'معدل الفوز:' : 'Win Rate:'}</span>
                          <span className="text-yellow-400">{(metrics.winRate * 100).toFixed(1)}%</span>
                        </div>
                        <Progress value={metrics.winRate * 100} className="h-2" />
                      </div>

                      {/* إحصائيات إضافية */}
                      <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-slate-700">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">{metrics.totalTrades}</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'إجمالي الصفقات' : 'Total Trades'}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-400">{metrics.successfulTrades}</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'صفقات ناجحة' : 'Successful'}</div>
                        </div>
                      </div>

                      {/* نتائج الباك تيست */}
                      <div className="mt-4 p-3 bg-slate-700 rounded-lg">
                        <h4 className="font-medium text-white mb-2">{lang === 'ar' ? 'نتائج الباك تيست:' : 'Backtest Results:'}</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-400">{lang === 'ar' ? 'العائد الإجمالي:' : 'Total Return:'}</span>
                            <span className="text-green-400">{(metrics.backtestResults.totalReturn * 100).toFixed(1)}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-400">{lang === 'ar' ? 'التقلبات:' : 'Volatility:'}</span>
                            <span className="text-yellow-400">{(metrics.backtestResults.volatility * 100).toFixed(1)}%</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* تبويب التنبؤات */}
          <TabsContent value="predictions" className="space-y-6">
            {prediction ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* التنبؤ الرئيسي */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-400" />
                      {lang === 'ar' ? 'التنبؤ الحالي' : 'Current Prediction'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* الاتجاه والثقة */}
                      <div className="text-center">
                        <div className={`text-4xl font-bold mb-2 ${getDirectionColor(prediction.prediction.direction)}`}>
                          {getDirectionText(prediction.prediction.direction)}
                        </div>
                        <div className="text-lg text-gray-400">
                          {lang === 'ar' ? 'الثقة:' : 'Confidence:'} {(prediction.prediction.confidence * 100).toFixed(1)}%
                        </div>
                        <Progress value={prediction.prediction.confidence * 100} className="h-3 mt-2" />
                      </div>

                      {/* الاحتماليات */}
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-400">{lang === 'ar' ? 'صعودي:' : 'Bullish:'}</span>
                            <span className="text-green-400">{(prediction.prediction.probability.bullish * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={prediction.prediction.probability.bullish * 100} className="h-2" />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-400">{lang === 'ar' ? 'هبوطي:' : 'Bearish:'}</span>
                            <span className="text-red-400">{(prediction.prediction.probability.bearish * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={prediction.prediction.probability.bearish * 100} className="h-2" />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-400">{lang === 'ar' ? 'محايد:' : 'Neutral:'}</span>
                            <span className="text-yellow-400">{(prediction.prediction.probability.neutral * 100).toFixed(1)}%</span>
                          </div>
                          <Progress value={prediction.prediction.probability.neutral * 100} className="h-2" />
                        </div>
                      </div>

                      {/* الأهداف */}
                      <div className="grid grid-cols-3 gap-2 mt-4 pt-4 border-t border-slate-700">
                        <div className="text-center">
                          <div className="text-sm text-gray-400">{lang === 'ar' ? 'الهدف' : 'Target'}</div>
                          <div className="text-green-400 font-bold">{prediction.prediction.priceTarget.toFixed(5)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-gray-400">{lang === 'ar' ? 'وقف الخسارة' : 'Stop Loss'}</div>
                          <div className="text-red-400 font-bold">{prediction.prediction.stopLoss.toFixed(5)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-gray-400">{lang === 'ar' ? 'جني الأرباح' : 'Take Profit'}</div>
                          <div className="text-blue-400 font-bold">{prediction.prediction.takeProfit.toFixed(5)}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* التحليل الفني */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-green-400" />
                      {lang === 'ar' ? 'التحليل الفني' : 'Technical Analysis'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* RSI */}
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-gray-400">RSI:</span>
                          <span className="text-white">{prediction.technicalAnalysis.rsi.toFixed(1)}</span>
                        </div>
                        <Progress value={prediction.technicalAnalysis.rsi} className="h-2" />
                      </div>

                      {/* MACD */}
                      <div className="grid grid-cols-3 gap-2">
                        <div className="text-center">
                          <div className="text-xs text-gray-400">MACD</div>
                          <div className="text-white text-sm">{prediction.technicalAnalysis.macd.macd.toFixed(4)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'الإشارة' : 'Signal'}</div>
                          <div className="text-white text-sm">{prediction.technicalAnalysis.macd.signal.toFixed(4)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'الهيستوجرام' : 'Histogram'}</div>
                          <div className="text-white text-sm">{prediction.technicalAnalysis.macd.histogram.toFixed(4)}</div>
                        </div>
                      </div>

                      {/* EMAs */}
                      <div className="grid grid-cols-3 gap-2">
                        <div className="text-center">
                          <div className="text-xs text-gray-400">EMA 20</div>
                          <div className="text-blue-400 text-sm">{prediction.technicalAnalysis.ema.ema20.toFixed(5)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-gray-400">EMA 50</div>
                          <div className="text-purple-400 text-sm">{prediction.technicalAnalysis.ema.ema50.toFixed(5)}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xs text-gray-400">EMA 200</div>
                          <div className="text-yellow-400 text-sm">{prediction.technicalAnalysis.ema.ema200.toFixed(5)}</div>
                        </div>
                      </div>

                      {/* مستويات الدعم والمقاومة */}
                      <div className="grid grid-cols-2 gap-4 mt-4 pt-4 border-t border-slate-700">
                        <div>
                          <h4 className="text-sm font-medium text-green-400 mb-2">{lang === 'ar' ? 'مستويات الدعم' : 'Support Levels'}</h4>
                          {prediction.technicalAnalysis.support.slice(0, 3).map((level, i) => (
                            <div key={i} className="text-xs text-gray-300">{level.toFixed(5)}</div>
                          ))}
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-red-400 mb-2">{lang === 'ar' ? 'مستويات المقاومة' : 'Resistance Levels'}</h4>
                          {prediction.technicalAnalysis.resistance.slice(0, 3).map((level, i) => (
                            <div key={i} className="text-xs text-gray-300">{level.toFixed(5)}</div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* مقاييس المخاطر */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5 text-red-400" />
                      {lang === 'ar' ? 'مقاييس المخاطر' : 'Risk Metrics'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-red-400">{(prediction.riskMetrics.volatility * 100).toFixed(1)}%</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'التقلبات' : 'Volatility'}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">{prediction.riskMetrics.sharpeRatio.toFixed(2)}</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'نسبة شارب' : 'Sharpe Ratio'}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-400">{(prediction.riskMetrics.maxDrawdown * 100).toFixed(1)}%</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'أقصى انخفاض' : 'Max Drawdown'}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-400">{(prediction.riskMetrics.winRate * 100).toFixed(1)}%</div>
                          <div className="text-xs text-gray-400">{lang === 'ar' ? 'معدل الفوز' : 'Win Rate'}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* معلومات التنفيذ */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-gray-400" />
                      {lang === 'ar' ? 'معلومات التنفيذ' : 'Execution Info'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'النموذج:' : 'Model:'}</span>
                        <span className="text-white">{prediction.modelId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'زوج العملة:' : 'Trading Pair:'}</span>
                        <span className="text-white">{prediction.tradingPair}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'الإطار الزمني:' : 'Timeframe:'}</span>
                        <span className="text-white">{prediction.timeframe}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'وقت التنفيذ:' : 'Execution Time:'}</span>
                        <span className="text-white">{prediction.executionTime}ms</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'الطابع الزمني:' : 'Timestamp:'}</span>
                        <span className="text-white">{new Date(prediction.timestamp).toLocaleTimeString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-12 text-center">
                  <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {lang === 'ar' ? 'لا توجد تنبؤات' : 'No Predictions'}
                  </h3>
                  <p className="text-gray-400 mb-4">
                    {lang === 'ar' ? 'اختر نموذجاً وانقر على "تنبؤ" لإنشاء تنبؤ جديد' : 'Select a model and click "Predict" to generate a new prediction'}
                  </p>
                  <Button onClick={handleGeneratePrediction} disabled={!selectedModel || isLoading}>
                    <Target className="h-4 w-4 mr-2" />
                    {lang === 'ar' ? 'إنشاء تنبؤ' : 'Generate Prediction'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* تبويب المجموعة */}
          <TabsContent value="ensemble" className="space-y-6">
            {ensemblePrediction ? (
              <div className="space-y-6">
                {/* التوصية النهائية */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-400" />
                      {lang === 'ar' ? 'التوصية النهائية للمجموعة' : 'Final Ensemble Recommendation'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center">
                      <div className={`inline-flex items-center px-6 py-3 rounded-full text-white font-bold text-2xl mb-4 ${getRecommendationColor(ensemblePrediction.recommendedAction)}`}>
                        {getRecommendationText(ensemblePrediction.recommendedAction)}
                      </div>

                      <div className="grid grid-cols-2 gap-6 mt-6">
                        <div>
                          <div className="text-3xl font-bold text-blue-400 mb-2">
                            {(ensemblePrediction.consensusScore * 100).toFixed(1)}%
                          </div>
                          <div className="text-gray-400">{lang === 'ar' ? 'درجة الإجماع' : 'Consensus Score'}</div>
                          <Progress value={ensemblePrediction.consensusScore * 100} className="h-3 mt-2" />
                        </div>

                        <div>
                          <div className="text-3xl font-bold text-yellow-400 mb-2">
                            {(ensemblePrediction.disagreementLevel * 100).toFixed(1)}%
                          </div>
                          <div className="text-gray-400">{lang === 'ar' ? 'مستوى الخلاف' : 'Disagreement Level'}</div>
                          <Progress value={ensemblePrediction.disagreementLevel * 100} className="h-3 mt-2" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* مساهمات النماذج */}
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5 text-green-400" />
                      {lang === 'ar' ? 'مساهمات النماذج' : 'Model Contributions'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {ensemblePrediction.modelContributions.map((contrib, index) => (
                        <div key={index} className="p-4 bg-slate-700 rounded-lg">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium text-white">{contrib.modelId.replace(/_/g, ' ').toUpperCase()}</h4>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">
                                {lang === 'ar' ? 'الوزن:' : 'Weight:'} {(contrib.weight * 100).toFixed(1)}%
                              </Badge>
                              <Badge variant="secondary">
                                {lang === 'ar' ? 'الدقة:' : 'Accuracy:'} {(contrib.accuracy * 100).toFixed(1)}%
                              </Badge>
                            </div>
                          </div>

                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center">
                              <div className={`text-lg font-bold ${getDirectionColor(contrib.prediction.prediction.direction)}`}>
                                {getDirectionText(contrib.prediction.prediction.direction)}
                              </div>
                              <div className="text-xs text-gray-400">{lang === 'ar' ? 'الاتجاه' : 'Direction'}</div>
                            </div>

                            <div className="text-center">
                              <div className="text-lg font-bold text-white">
                                {(contrib.prediction.prediction.confidence * 100).toFixed(1)}%
                              </div>
                              <div className="text-xs text-gray-400">{lang === 'ar' ? 'الثقة' : 'Confidence'}</div>
                            </div>

                            <div className="text-center">
                              <div className="text-lg font-bold text-blue-400">
                                {contrib.prediction.executionTime}ms
                              </div>
                              <div className="text-xs text-gray-400">{lang === 'ar' ? 'وقت التنفيذ' : 'Execution Time'}</div>
                            </div>
                          </div>

                          <div className="mt-3">
                            <div className="text-xs text-gray-400 mb-1">{lang === 'ar' ? 'مساهمة الوزن:' : 'Weight Contribution:'}</div>
                            <Progress value={contrib.weight * 100} className="h-2" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card className="bg-slate-800 border-slate-700">
                <CardContent className="p-12 text-center">
                  <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {lang === 'ar' ? 'لا يوجد تنبؤ مجمع' : 'No Ensemble Prediction'}
                  </h3>
                  <p className="text-gray-400 mb-4">
                    {lang === 'ar' ? 'انقر على "تنبؤ مجمع" لإنشاء تنبؤ من جميع النماذج' : 'Click "Ensemble Prediction" to generate a prediction from all models'}
                  </p>
                  <Button onClick={handleGenerateEnsemble} disabled={isLoading}>
                    <Target className="h-4 w-4 mr-2" />
                    {lang === 'ar' ? 'إنشاء تنبؤ مجمع' : 'Generate Ensemble Prediction'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default HybridModels;
