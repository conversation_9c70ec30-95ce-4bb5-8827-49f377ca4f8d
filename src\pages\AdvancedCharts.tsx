import React, { useState, useEffect } from 'react';
import { Tren<PERSON>Up, <PERSON><PERSON>hart3, <PERSON><PERSON>s, Refresh<PERSON><PERSON>, Eye, EyeOff, Palette, Download } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import AdvancedCandlestickChart from '@/components/charts/AdvancedCandlestickChart';
import SimpleCandlestickChart from '@/components/charts/SimpleCandlestickChart';
import CanvasCandlestickChart from '@/components/charts/CanvasCandlestickChart';
import D3CandlestickChart from '@/components/charts/D3CandlestickChart';
import { enhancedMarketDataService, MarketData } from '@/services/enhancedMarketDataService';

interface AdvancedChartsProps {
  lang?: 'en' | 'ar';
}

const AdvancedCharts: React.FC<AdvancedChartsProps> = ({ lang = 'ar' }) => {
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [selectedSymbol, setSelectedSymbol] = useState('EURUSD');
  const [selectedTimeframe, setSelectedTimeframe] = useState('1h');
  const [isLoading, setIsLoading] = useState(false);
  const [chartSettings, setChartSettings] = useState({
    showVolume: true,
    showIndicators: true,
    showSupportResistance: true,
    theme: 'dark' as 'light' | 'dark',
    chartHeight: 600,
    chartType: 'canvas' as 'simple' | 'advanced' | 'canvas' | 'd3' // نوع الرسم البياني
  });

  const symbols = enhancedMarketDataService.getAvailableSymbols();
  const timeframes = enhancedMarketDataService.getAvailableTimeframes();

  useEffect(() => {
    loadMarketData();
  }, [selectedSymbol, selectedTimeframe]);

  const loadMarketData = async () => {
    setIsLoading(true);
    try {
      console.log(`Loading market data for ${selectedSymbol} ${selectedTimeframe}`);
      const data = await enhancedMarketDataService.getMarketData(selectedSymbol, selectedTimeframe);
      console.log('Market data loaded:', data);
      setMarketData(data);
    } catch (error) {
      console.error('Error loading market data:', error);
      // إنشاء بيانات افتراضية في حالة الخطأ
      const fallbackData = {
        symbol: selectedSymbol,
        timeframe: selectedTimeframe,
        candlesticks: generateFallbackData(),
        indicators: [],
        supportLevels: [],
        resistanceLevels: [],
        trendLines: []
      };
      setMarketData(fallbackData);
    } finally {
      setIsLoading(false);
    }
  };

  const generateFallbackData = () => {
    const data = [];
    const basePrice = 1.0850;
    let currentPrice = basePrice;
    const now = Date.now();

    for (let i = 0; i < 100; i++) {
      const timestamp = now - (100 - i) * 60000; // كل دقيقة
      const change = (Math.random() - 0.5) * 0.01;
      const open = currentPrice;
      const close = open + change;
      const high = Math.max(open, close) + Math.random() * 0.005;
      const low = Math.min(open, close) - Math.random() * 0.005;
      const volume = 1000000 + Math.random() * 500000;

      data.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume,
        date: new Date(timestamp)
      });

      currentPrice = close;
    }

    return data;
  };

  const handleRefreshData = async () => {
    setIsLoading(true);
    try {
      console.log(`Refreshing data for ${selectedSymbol} ${selectedTimeframe}`);
      const data = await enhancedMarketDataService.refreshData(selectedSymbol, selectedTimeframe);
      setMarketData(data);
    } catch (error) {
      console.error('Error refreshing data:', error);
      // إعادة تحميل البيانات الأساسية
      await loadMarketData();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setChartSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const getSymbolInfo = (symbol: string) => {
    const info: Record<string, { name: string; type: string; market: string }> = {
      'EURUSD': { name: 'يورو/دولار', type: 'فوركس', market: 'FX' },
      'GBPUSD': { name: 'جنيه/دولار', type: 'فوركس', market: 'FX' },
      'USDJPY': { name: 'دولار/ين', type: 'فوركس', market: 'FX' },
      'AUDUSD': { name: 'دولار أسترالي/دولار', type: 'فوركس', market: 'FX' },
      'USDCAD': { name: 'دولار/دولار كندي', type: 'فوركس', market: 'FX' },
      'BTCUSDT': { name: 'بيتكوين/تيثر', type: 'عملة رقمية', market: 'CRYPTO' },
      'ETHUSDT': { name: 'إيثريوم/تيثر', type: 'عملة رقمية', market: 'CRYPTO' },
      'XAUUSD': { name: 'ذهب/دولار', type: 'معادن ثمينة', market: 'COMMODITIES' }
    };
    return info[symbol] || { name: symbol, type: 'غير محدد', market: 'OTHER' };
  };

  const getCurrentPrice = () => {
    if (!marketData || marketData.candlesticks.length === 0) return 0;
    return marketData.candlesticks[marketData.candlesticks.length - 1].close;
  };

  const getPriceChange = () => {
    if (!marketData || marketData.candlesticks.length < 2) return { change: 0, changePercent: 0 };
    
    const current = marketData.candlesticks[marketData.candlesticks.length - 1].close;
    const previous = marketData.candlesticks[marketData.candlesticks.length - 2].close;
    const change = current - previous;
    const changePercent = (change / previous) * 100;
    
    return { change, changePercent };
  };

  const symbolInfo = getSymbolInfo(selectedSymbol);
  const currentPrice = getCurrentPrice();
  const { change, changePercent } = getPriceChange();

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* العنوان الرئيسي */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <BarChart3 className="h-8 w-8 text-blue-400" />
            {lang === 'ar' ? 'الرسوم البيانية المتقدمة' : 'Advanced Charts'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar' 
              ? 'رسوم الشموع اليابانية المتقدمة مع المؤشرات الفنية ومستويات الدعم والمقاومة'
              : 'Advanced candlestick charts with technical indicators and support/resistance levels'
            }
          </p>
        </div>

        {/* شريط التحكم */}
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
              {/* اختيار الرمز */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'الرمز' : 'Symbol'}
                </label>
                <Select value={selectedSymbol} onValueChange={setSelectedSymbol}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {symbols.map(symbol => (
                      <SelectItem key={symbol} value={symbol}>
                        <div className="flex items-center gap-2">
                          <span className="font-mono">{symbol}</span>
                          <Badge variant="outline" className="text-xs">
                            {getSymbolInfo(symbol).market}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* اختيار الإطار الزمني */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'الإطار الزمني' : 'Timeframe'}
                </label>
                <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {timeframes.map(tf => (
                      <SelectItem key={tf} value={tf}>{tf}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* معلومات السعر */}
              <div className="space-y-1">
                <div className="text-sm text-gray-400">{lang === 'ar' ? 'السعر الحالي' : 'Current Price'}</div>
                <div className="text-lg font-bold text-white">
                  {currentPrice.toFixed(selectedSymbol.includes('JPY') ? 3 : 5)}
                </div>
              </div>

              {/* التغيير */}
              <div className="space-y-1">
                <div className="text-sm text-gray-400">{lang === 'ar' ? 'التغيير' : 'Change'}</div>
                <div className={`text-lg font-bold ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {change >= 0 ? '+' : ''}{change.toFixed(selectedSymbol.includes('JPY') ? 3 : 5)}
                  <span className="text-sm ml-1">
                    ({changePercent >= 0 ? '+' : ''}{changePercent.toFixed(2)}%)
                  </span>
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex gap-2">
                <Button
                  onClick={handleRefreshData}
                  disabled={isLoading}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  {lang === 'ar' ? 'تحديث' : 'Refresh'}
                </Button>
              </div>

              {/* معلومات الرمز */}
              <div className="space-y-1">
                <div className="text-sm text-gray-400">{symbolInfo.name}</div>
                <Badge variant="secondary" className="text-xs">
                  {symbolInfo.type}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* التبويبات الرئيسية */}
        <Tabs defaultValue="chart" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800">
            <TabsTrigger value="chart" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الرسم البياني' : 'Chart'}
            </TabsTrigger>
            <TabsTrigger value="indicators" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'المؤشرات' : 'Indicators'}
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-slate-700">
              {lang === 'ar' ? 'الإعدادات' : 'Settings'}
            </TabsTrigger>
          </TabsList>

          {/* تبويب الرسم البياني */}
          <TabsContent value="chart" className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-400" />
                    {symbolInfo.name} - {selectedTimeframe}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={change >= 0 ? "default" : "destructive"}>
                      {change >= 0 ? '📈' : '📉'} {Math.abs(changePercent).toFixed(2)}%
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {marketData ? (
                  <div className="w-full overflow-x-auto">
                    {chartSettings.chartType === 'canvas' ? (
                      <CanvasCandlestickChart
                        data={marketData.candlesticks}
                        indicators={chartSettings.showIndicators ? marketData.indicators : []}
                        supportLevels={chartSettings.showSupportResistance ? marketData.supportLevels : []}
                        resistanceLevels={chartSettings.showSupportResistance ? marketData.resistanceLevels : []}
                        symbol={selectedSymbol}
                        timeframe={selectedTimeframe}
                        height={chartSettings.chartHeight}
                        width={1200}
                        showVolume={chartSettings.showVolume}
                        theme={chartSettings.theme}
                      />
                    ) : chartSettings.chartType === 'd3' ? (
                      <D3CandlestickChart
                        data={marketData.candlesticks}
                        indicators={chartSettings.showIndicators ? marketData.indicators : []}
                        supportLevels={chartSettings.showSupportResistance ? marketData.supportLevels : []}
                        resistanceLevels={chartSettings.showSupportResistance ? marketData.resistanceLevels : []}
                        symbol={selectedSymbol}
                        timeframe={selectedTimeframe}
                        height={chartSettings.chartHeight}
                        width={1200}
                        showVolume={chartSettings.showVolume}
                        theme={chartSettings.theme}
                      />
                    ) : chartSettings.chartType === 'simple' ? (
                      <SimpleCandlestickChart
                        data={marketData.candlesticks}
                        symbol={selectedSymbol}
                        timeframe={selectedTimeframe}
                        height={chartSettings.chartHeight}
                        width={1200}
                      />
                    ) : (
                      <AdvancedCandlestickChart
                        data={marketData.candlesticks}
                        indicators={chartSettings.showIndicators ? marketData.indicators : []}
                        supportLevels={chartSettings.showSupportResistance ? marketData.supportLevels : []}
                        resistanceLevels={chartSettings.showSupportResistance ? marketData.resistanceLevels : []}
                        trendLines={marketData.trendLines}
                        symbol={selectedSymbol}
                        timeframe={selectedTimeframe}
                        height={chartSettings.chartHeight}
                        showVolume={chartSettings.showVolume}
                        showIndicators={chartSettings.showIndicators}
                        theme={chartSettings.theme}
                      />
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-96">
                    <div className="text-center">
                      <RefreshCw className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-spin" />
                      <p className="text-gray-400">
                        {lang === 'ar' ? 'جاري تحميل البيانات...' : 'Loading data...'}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* تبويب المؤشرات */}
          <TabsContent value="indicators" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {marketData?.indicators.map((indicator, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <div 
                        className="w-4 h-4 rounded-full" 
                        style={{ backgroundColor: indicator.color }}
                      ></div>
                      {indicator.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'القيمة الحالية:' : 'Current Value:'}</span>
                        <span className="text-white font-mono">
                          {indicator.values[indicator.values.length - 1]?.toFixed(2) || 'N/A'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'النوع:' : 'Type:'}</span>
                        <Badge variant="outline">{indicator.type}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{lang === 'ar' ? 'نقاط البيانات:' : 'Data Points:'}</span>
                        <span className="text-white">{indicator.values.length}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* تبويب الإعدادات */}
          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* إعدادات العرض */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5 text-green-400" />
                    {lang === 'ar' ? 'إعدادات العرض' : 'Display Settings'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'عرض الحجم' : 'Show Volume'}
                    </label>
                    <Switch
                      checked={chartSettings.showVolume}
                      onCheckedChange={(checked) => handleSettingChange('showVolume', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'عرض المؤشرات' : 'Show Indicators'}
                    </label>
                    <Switch
                      checked={chartSettings.showIndicators}
                      onCheckedChange={(checked) => handleSettingChange('showIndicators', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'مستويات الدعم والمقاومة' : 'Support/Resistance'}
                    </label>
                    <Switch
                      checked={chartSettings.showSupportResistance}
                      onCheckedChange={(checked) => handleSettingChange('showSupportResistance', checked)}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'نوع الرسم البياني' : 'Chart Type'}
                    </label>
                    <Select
                      value={chartSettings.chartType}
                      onValueChange={(value: 'simple' | 'advanced' | 'canvas' | 'd3') => handleSettingChange('chartType', value)}
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="canvas">{lang === 'ar' ? 'شموع تفاعلية (Canvas)' : 'Interactive Candlesticks (Canvas)'}</SelectItem>
                        <SelectItem value="d3">{lang === 'ar' ? 'شموع احترافية (D3.js)' : 'Professional Candlesticks (D3.js)'}</SelectItem>
                        <SelectItem value="simple">{lang === 'ar' ? 'شموع بسيطة (SVG)' : 'Simple Candlesticks (SVG)'}</SelectItem>
                        <SelectItem value="advanced">{lang === 'ar' ? 'رسم متقدم (Chart.js)' : 'Advanced Chart (Chart.js)'}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* إعدادات المظهر */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="h-5 w-5 text-purple-400" />
                    {lang === 'ar' ? 'إعدادات المظهر' : 'Appearance Settings'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'المظهر' : 'Theme'}
                    </label>
                    <Select 
                      value={chartSettings.theme} 
                      onValueChange={(value: 'light' | 'dark') => handleSettingChange('theme', value)}
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="dark">{lang === 'ar' ? 'داكن' : 'Dark'}</SelectItem>
                        <SelectItem value="light">{lang === 'ar' ? 'فاتح' : 'Light'}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      {lang === 'ar' ? 'ارتفاع الرسم البياني' : 'Chart Height'}
                    </label>
                    <Select 
                      value={chartSettings.chartHeight.toString()} 
                      onValueChange={(value) => handleSettingChange('chartHeight', parseInt(value))}
                    >
                      <SelectTrigger className="bg-slate-700 border-slate-600">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="400">400px</SelectItem>
                        <SelectItem value="500">500px</SelectItem>
                        <SelectItem value="600">600px</SelectItem>
                        <SelectItem value="700">700px</SelectItem>
                        <SelectItem value="800">800px</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* معلومات إضافية */}
        {marketData && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-sm">
                  {lang === 'ar' ? 'مستويات الدعم' : 'Support Levels'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {marketData.supportLevels.map((level, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-gray-400">S{index + 1}:</span>
                      <span className="text-green-400 font-mono">
                        {level.toFixed(selectedSymbol.includes('JPY') ? 3 : 5)}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-sm">
                  {lang === 'ar' ? 'مستويات المقاومة' : 'Resistance Levels'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {marketData.resistanceLevels.map((level, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="text-gray-400">R{index + 1}:</span>
                      <span className="text-red-400 font-mono">
                        {level.toFixed(selectedSymbol.includes('JPY') ? 3 : 5)}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-sm">
                  {lang === 'ar' ? 'إحصائيات البيانات' : 'Data Statistics'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">{lang === 'ar' ? 'عدد الشموع:' : 'Candles:'}</span>
                    <span className="text-white">{marketData.candlesticks.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">{lang === 'ar' ? 'المؤشرات:' : 'Indicators:'}</span>
                    <span className="text-white">{marketData.indicators.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">{lang === 'ar' ? 'آخر تحديث:' : 'Last Update:'}</span>
                    <span className="text-white text-xs">
                      {new Date().toLocaleTimeString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedCharts;
