import React, { useRef, useEffect, useState } from 'react';
import { CandlestickData, TechnicalIndicator } from '@/services/enhancedMarketDataService';
import { DetectedPattern } from '@/services/patternDetectionService';
import PatternOverlay from './PatternOverlay';

interface CanvasCandlestickChartProps {
  data: CandlestickData[];
  indicators?: TechnicalIndicator[];
  supportLevels?: number[];
  resistanceLevels?: number[];
  patterns?: DetectedPattern[];
  symbol: string;
  timeframe: string;
  height?: number;
  width?: number;
  showVolume?: boolean;
  theme?: 'light' | 'dark';
  onPatternClick?: (pattern: DetectedPattern) => void;
}

const CanvasCandlestickChart: React.FC<CanvasCandlestickChartProps> = ({
  data,
  indicators = [],
  supportLevels = [],
  resistanceLevels = [],
  patterns = [],
  symbol,
  timeframe,
  height = 600,
  width = 1200,
  showVolume = true,
  theme = 'dark',
  onPatternClick
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredCandle, setHoveredCandle] = useState<number | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (data && data.length > 0) {
      drawChart();
    }
  }, [data, indicators, supportLevels, resistanceLevels, patterns, theme, hoveredCandle]);

  const drawChart = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // إعداد الألوان حسب المظهر
    const colors = theme === 'dark' ? {
      background: '#0F172A',
      grid: '#334155',
      text: '#F1F5F9',
      bullish: '#10B981',
      bearish: '#EF4444',
      volume: '#64748B'
    } : {
      background: '#FFFFFF',
      grid: '#E2E8F0',
      text: '#1E293B',
      bullish: '#059669',
      bearish: '#DC2626',
      volume: '#64748B'
    };

    // مسح الكانفاس
    ctx.fillStyle = colors.background;
    ctx.fillRect(0, 0, width, height);

    // حساب الأبعاد
    const chartArea = {
      left: 80,
      right: width - 50,
      top: 50,
      bottom: showVolume ? height - 150 : height - 50
    };

    const volumeArea = showVolume ? {
      left: 80,
      right: width - 50,
      top: height - 140,
      bottom: height - 50
    } : null;

    // حساب القيم
    const prices = data.map(d => [d.open, d.high, d.low, d.close]).flat();
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;
    const padding = priceRange * 0.05;

    const volumes = data.map(d => d.volume);
    const maxVolume = Math.max(...volumes);

    // دوال التحويل
    const priceToY = (price: number) => {
      return chartArea.top + ((maxPrice + padding - price) / (priceRange + 2 * padding)) * (chartArea.bottom - chartArea.top);
    };

    const indexToX = (index: number) => {
      return chartArea.left + (index / (data.length - 1)) * (chartArea.right - chartArea.left);
    };

    const volumeToY = (volume: number) => {
      if (!volumeArea) return 0;
      return volumeArea.bottom - (volume / maxVolume) * (volumeArea.bottom - volumeArea.top);
    };

    // رسم الشبكة
    ctx.strokeStyle = colors.grid;
    ctx.lineWidth = 1;
    ctx.setLineDash([]);

    // خطوط أفقية للأسعار
    for (let i = 0; i <= 10; i++) {
      const price = minPrice + (priceRange * i / 10);
      const y = priceToY(price);
      
      ctx.beginPath();
      ctx.moveTo(chartArea.left, y);
      ctx.lineTo(chartArea.right, y);
      ctx.stroke();

      // تسميات الأسعار
      ctx.fillStyle = colors.text;
      ctx.font = '12px Arial';
      ctx.textAlign = 'right';
      ctx.fillText(price.toFixed(5), chartArea.left - 10, y + 4);
    }

    // خطوط عمودية للوقت
    const timeStep = Math.max(1, Math.floor(data.length / 8));
    for (let i = 0; i < data.length; i += timeStep) {
      const x = indexToX(i);
      
      ctx.beginPath();
      ctx.moveTo(x, chartArea.top);
      ctx.lineTo(x, chartArea.bottom);
      ctx.stroke();

      // تسميات الوقت
      const date = new Date(data[i].timestamp);
      const timeStr = date.toLocaleTimeString('ar-SA', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      ctx.fillStyle = colors.text;
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(timeStr, x, chartArea.bottom + 20);
    }

    // رسم مستويات الدعم والمقاومة
    ctx.setLineDash([5, 5]);
    
    supportLevels.forEach(level => {
      const y = priceToY(level);
      ctx.strokeStyle = colors.bullish;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(chartArea.left, y);
      ctx.lineTo(chartArea.right, y);
      ctx.stroke();
    });

    resistanceLevels.forEach(level => {
      const y = priceToY(level);
      ctx.strokeStyle = colors.bearish;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(chartArea.left, y);
      ctx.lineTo(chartArea.right, y);
      ctx.stroke();
    });

    ctx.setLineDash([]);

    // رسم المؤشرات الفنية
    indicators.forEach(indicator => {
      if (indicator.type === 'line' && indicator.values.length > 0) {
        ctx.strokeStyle = indicator.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        let started = false;
        indicator.values.forEach((value, index) => {
          if (!isNaN(value) && index < data.length) {
            const x = indexToX(index);
            const y = priceToY(value);
            
            if (!started) {
              ctx.moveTo(x, y);
              started = true;
            } else {
              ctx.lineTo(x, y);
            }
          }
        });
        
        ctx.stroke();
      }
    });

    // رسم الشموع اليابانية
    const candleWidth = Math.max(2, (chartArea.right - chartArea.left) / data.length - 2);
    
    data.forEach((candle, index) => {
      const x = indexToX(index);
      const openY = priceToY(candle.open);
      const closeY = priceToY(candle.close);
      const highY = priceToY(candle.high);
      const lowY = priceToY(candle.low);

      const isBullish = candle.close > candle.open;
      const color = isBullish ? colors.bullish : colors.bearish;
      const isHovered = hoveredCandle === index;

      // رسم الذيول
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();

      // رسم الجسم
      const bodyTop = Math.min(openY, closeY);
      const bodyBottom = Math.max(openY, closeY);
      const bodyHeight = Math.max(1, bodyBottom - bodyTop);

      ctx.fillStyle = isHovered ? color : (isBullish ? color : colors.background);
      ctx.strokeStyle = color;
      ctx.lineWidth = isHovered ? 2 : 1;

      ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
      ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);
    });

    // رسم الحجم
    if (showVolume && volumeArea) {
      data.forEach((candle, index) => {
        const x = indexToX(index);
        const volumeY = volumeToY(candle.volume);
        const isBullish = candle.close > candle.open;
        
        ctx.fillStyle = isBullish ? colors.bullish + '80' : colors.bearish + '80';
        ctx.fillRect(x - candleWidth / 2, volumeY, candleWidth, volumeArea.bottom - volumeY);
      });

      // تسمية محور الحجم
      ctx.fillStyle = colors.text;
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('الحجم', chartArea.left + (chartArea.right - chartArea.left) / 2, height - 20);
    }

    // العنوان
    ctx.fillStyle = colors.text;
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${symbol} - ${timeframe}`, width / 2, 30);

    // معلومات الشمعة المحددة
    if (hoveredCandle !== null && data[hoveredCandle]) {
      const candle = data[hoveredCandle];
      const tooltipX = mousePos.x + 10;
      const tooltipY = mousePos.y - 10;

      // خلفية التولتيب
      ctx.fillStyle = colors.background + 'E6';
      ctx.strokeStyle = colors.grid;
      ctx.lineWidth = 1;
      const tooltipWidth = 200;
      const tooltipHeight = 120;
      
      ctx.fillRect(tooltipX, tooltipY - tooltipHeight, tooltipWidth, tooltipHeight);
      ctx.strokeRect(tooltipX, tooltipY - tooltipHeight, tooltipWidth, tooltipHeight);

      // نص التولتيب
      ctx.fillStyle = colors.text;
      ctx.font = '12px Arial';
      ctx.textAlign = 'left';
      
      const lines = [
        `الوقت: ${new Date(candle.timestamp).toLocaleString('ar-SA')}`,
        `فتح: ${candle.open.toFixed(5)}`,
        `أعلى: ${candle.high.toFixed(5)}`,
        `أقل: ${candle.low.toFixed(5)}`,
        `إغلاق: ${candle.close.toFixed(5)}`,
        `الحجم: ${(candle.volume / 1000000).toFixed(2)}M`
      ];

      lines.forEach((line, i) => {
        ctx.fillText(line, tooltipX + 10, tooltipY - tooltipHeight + 20 + (i * 16));
      });
    }
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setMousePos({ x, y });

    // تحديد الشمعة المحددة
    const chartArea = {
      left: 80,
      right: canvas.width - 50,
      top: 50,
      bottom: showVolume ? canvas.height - 150 : canvas.height - 50
    };

    if (x >= chartArea.left && x <= chartArea.right && y >= chartArea.top && y <= chartArea.bottom) {
      const relativeX = (x - chartArea.left) / (chartArea.right - chartArea.left);
      const candleIndex = Math.floor(relativeX * data.length);
      
      if (candleIndex >= 0 && candleIndex < data.length) {
        setHoveredCandle(candleIndex);
      } else {
        setHoveredCandle(null);
      }
    } else {
      setHoveredCandle(null);
    }
  };

  const handleMouseLeave = () => {
    setHoveredCandle(null);
  };

  // حساب نطاق الأسعار للأنماط
  const prices = data.map(d => [d.open, d.high, d.low, d.close]).flat();
  const priceRange = {
    min: Math.min(...prices),
    max: Math.max(...prices)
  };

  return (
    <div className="relative">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="border border-slate-600 rounded-lg cursor-crosshair"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      />

      {/* عرض الأنماط */}
      {patterns && patterns.length > 0 && (
        <PatternOverlay
          patterns={patterns}
          width={width}
          height={height}
          dataLength={data.length}
          priceRange={priceRange}
          onPatternClick={onPatternClick}
        />
      )}

      {/* معلومات إضافية */}
      <div className="absolute top-4 left-4 bg-slate-800 bg-opacity-90 rounded-lg p-3 text-white text-sm">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span>صعود</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span>هبوط</span>
          </div>
          <div className="text-gray-400">
            الشموع: {data.length}
          </div>
          {patterns && patterns.length > 0 && (
            <div className="text-blue-400">
              الأنماط: {patterns.length}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CanvasCandlestickChart;
