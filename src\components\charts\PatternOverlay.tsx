import React from 'react';
import { DetectedPattern } from '@/services/patternDetectionService';

interface PatternOverlayProps {
  patterns: DetectedPattern[];
  width: number;
  height: number;
  dataLength: number;
  priceRange: { min: number; max: number };
  onPatternClick?: (pattern: DetectedPattern) => void;
}

const PatternOverlay: React.FC<PatternOverlayProps> = ({
  patterns,
  width,
  height,
  dataLength,
  priceRange,
  onPatternClick
}) => {
  const chartArea = {
    left: 80,
    right: width - 50,
    top: 50,
    bottom: height - 150
  };

  const indexToX = (index: number) => {
    return chartArea.left + (index / (dataLength - 1)) * (chartArea.right - chartArea.left);
  };

  const priceToY = (price: number) => {
    const padding = (priceRange.max - priceRange.min) * 0.05;
    return chartArea.top + ((priceRange.max + padding - price) / (priceRange.max - priceRange.min + 2 * padding)) * (chartArea.bottom - chartArea.top);
  };

  const getPatternColor = (type: string) => {
    switch (type) {
      case 'bullish': return '#10B981';
      case 'bearish': return '#EF4444';
      case 'neutral': return '#F59E0B';
      case 'reversal': return '#8B5CF6';
      case 'continuation': return '#3B82F6';
      default: return '#6B7280';
    }
  };

  const getPatternShape = (pattern: DetectedPattern) => {
    const startX = indexToX(pattern.startIndex);
    const endX = indexToX(pattern.endIndex);
    const centerX = (startX + endX) / 2;
    const centerY = priceToY(pattern.coordinates[0]?.y || priceRange.min);
    const color = getPatternColor(pattern.type);

    switch (pattern.name) {
      case 'Doji':
        return (
          <g key={pattern.id}>
            <circle
              cx={centerX}
              cy={centerY}
              r="8"
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeDasharray="4,4"
            />
            <text
              x={centerX}
              y={centerY - 15}
              textAnchor="middle"
              fill={color}
              fontSize="10"
              fontWeight="bold"
            >
              DOJI
            </text>
          </g>
        );

      case 'Hammer':
      case 'Shooting Star':
        return (
          <g key={pattern.id}>
            <rect
              x={centerX - 6}
              y={centerY - 6}
              width="12"
              height="12"
              fill="none"
              stroke={color}
              strokeWidth="2"
              transform={`rotate(45 ${centerX} ${centerY})`}
            />
            <text
              x={centerX}
              y={centerY - 15}
              textAnchor="middle"
              fill={color}
              fontSize="9"
              fontWeight="bold"
            >
              {pattern.name === 'Hammer' ? 'HAM' : 'STAR'}
            </text>
          </g>
        );

      case 'Bullish Engulfing':
      case 'Bearish Engulfing':
        return (
          <g key={pattern.id}>
            <rect
              x={startX - 5}
              y={centerY - 8}
              width={endX - startX + 10}
              height="16"
              fill="none"
              stroke={color}
              strokeWidth="2"
              rx="4"
            />
            <text
              x={centerX}
              y={centerY - 20}
              textAnchor="middle"
              fill={color}
              fontSize="9"
              fontWeight="bold"
            >
              ENGULF
            </text>
          </g>
        );

      case 'Morning Star':
      case 'Evening Star':
        return (
          <g key={pattern.id}>
            <polygon
              points={`${centerX},${centerY - 10} ${centerX - 8},${centerY + 6} ${centerX + 8},${centerY + 6}`}
              fill="none"
              stroke={color}
              strokeWidth="2"
            />
            <text
              x={centerX}
              y={centerY - 15}
              textAnchor="middle"
              fill={color}
              fontSize="9"
              fontWeight="bold"
            >
              STAR
            </text>
          </g>
        );

      case 'Head and Shoulders':
      case 'Inverse Head and Shoulders':
        return (
          <g key={pattern.id}>
            <path
              d={`M ${startX} ${centerY} Q ${centerX} ${centerY - 20} ${endX} ${centerY}`}
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeDasharray="6,3"
            />
            <text
              x={centerX}
              y={centerY - 25}
              textAnchor="middle"
              fill={color}
              fontSize="8"
              fontWeight="bold"
            >
              H&S
            </text>
          </g>
        );

      case 'Double Top':
      case 'Double Bottom':
        return (
          <g key={pattern.id}>
            <ellipse
              cx={centerX}
              cy={centerY}
              rx={(endX - startX) / 2}
              ry="12"
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeDasharray="8,4"
            />
            <text
              x={centerX}
              y={centerY - 20}
              textAnchor="middle"
              fill={color}
              fontSize="9"
              fontWeight="bold"
            >
              {pattern.name.includes('Top') ? 'D.TOP' : 'D.BOT'}
            </text>
          </g>
        );

      case 'Ascending Triangle':
      case 'Descending Triangle':
        return (
          <g key={pattern.id}>
            <polygon
              points={`${startX},${centerY + 10} ${endX},${centerY - 10} ${endX},${centerY + 10}`}
              fill="none"
              stroke={color}
              strokeWidth="2"
            />
            <text
              x={centerX}
              y={centerY - 15}
              textAnchor="middle"
              fill={color}
              fontSize="8"
              fontWeight="bold"
            >
              TRI
            </text>
          </g>
        );

      case 'Bullish Flag':
      case 'Bearish Flag':
        return (
          <g key={pattern.id}>
            <rect
              x={startX}
              y={centerY - 8}
              width={endX - startX}
              height="16"
              fill="none"
              stroke={color}
              strokeWidth="2"
            />
            <line
              x1={startX}
              y1={centerY - 8}
              x2={startX}
              y2={centerY - 20}
              stroke={color}
              strokeWidth="2"
            />
            <text
              x={centerX}
              y={centerY - 25}
              textAnchor="middle"
              fill={color}
              fontSize="9"
              fontWeight="bold"
            >
              FLAG
            </text>
          </g>
        );

      default:
        return (
          <g key={pattern.id}>
            <circle
              cx={centerX}
              cy={centerY}
              r="6"
              fill={color}
              fillOpacity="0.3"
              stroke={color}
              strokeWidth="2"
            />
            <text
              x={centerX}
              y={centerY - 12}
              textAnchor="middle"
              fill={color}
              fontSize="8"
              fontWeight="bold"
            >
              {pattern.name.substring(0, 4).toUpperCase()}
            </text>
          </g>
        );
    }
  };

  return (
    <svg
      width={width}
      height={height}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        pointerEvents: 'none'
      }}
    >
      {patterns.map(pattern => (
        <g
          key={pattern.id}
          style={{ pointerEvents: 'all', cursor: 'pointer' }}
          onClick={() => onPatternClick?.(pattern)}
        >
          {getPatternShape(pattern)}
          
          {/* مؤشر الثقة */}
          <circle
            cx={indexToX(pattern.endIndex) + 15}
            cy={priceToY(pattern.coordinates[0]?.y || priceRange.min)}
            r="8"
            fill={pattern.confidence >= 80 ? '#10B981' : pattern.confidence >= 60 ? '#F59E0B' : '#EF4444'}
            fillOpacity="0.8"
          />
          <text
            x={indexToX(pattern.endIndex) + 15}
            y={priceToY(pattern.coordinates[0]?.y || priceRange.min) + 3}
            textAnchor="middle"
            fill="white"
            fontSize="8"
            fontWeight="bold"
          >
            {pattern.confidence}
          </text>
        </g>
      ))}
    </svg>
  );
};

export default PatternOverlay;
