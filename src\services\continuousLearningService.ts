
import * as tf from '@tensorflow/tfjs';

// واجهات البيانات المحسنة للتعلم المستمر
export interface ModelPerformance {
  modelId: string;
  modelName: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  loss: number;
  lastUpdated: number;
  trainingEpochs: number;
  dataPoints: number;
  performanceHistory: Array<{
    timestamp: number;
    accuracy: number;
    loss: number;
    epoch: number;
  }>;
}

export interface LearningMetrics {
  learningRate: number;
  convergenceRate: number;
  stabilityScore: number;
  adaptabilityScore: number;
  overfittingRisk: number;
  recommendedAction: 'continue' | 'retrain' | 'adjust_params' | 'collect_data';
}

export interface RetrainingTrigger {
  triggerId: string;
  type: 'performance_drop' | 'data_drift' | 'concept_drift' | 'scheduled' | 'manual';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: number;
  threshold: number;
  currentValue: number;
  actionTaken: boolean;
}

export interface AutoMLResult {
  bestModel: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  trainingTime: number;
  modelParams: Record<string, any>;
  validationMetrics: {
    crossValidationScore: number;
    overfittingRisk: number;
    generalizationScore: number;
  };
  recommendations: string[];
}

export interface OnlineLearningState {
  modelName: string;
  accuracy: number;
  dataProcessed: number;
  lastUpdate: number;
  driftDetected: boolean;
  performanceTrend: 'improving' | 'stable' | 'declining';
  adaptationRate: number;
  memoryUsage: number;
}

export interface ModelSelectionResult {
  selectedModel: string;
  confidence: number;
  performanceScore: number;
  reasons: string[];
  alternatives: Array<{
    model: string;
    score: number;
    pros: string[];
    cons: string[];
  }>;
  selectionCriteria: {
    accuracy: number;
    speed: number;
    memoryEfficiency: number;
    robustness: number;
  };
}

export interface HyperparameterOptimization {
  algorithm: 'random_search' | 'grid_search' | 'bayesian' | 'genetic';
  bestParams: Record<string, any>;
  bestScore: number;
  iterations: number;
  timeElapsed: number;
  improvementHistory: Array<{
    iteration: number;
    score: number;
    params: Record<string, any>;
  }>;
  convergenceStatus: 'converged' | 'improving' | 'plateaued';
}

export interface ContinuousLearningMetrics {
  totalModels: number;
  activeModels: number;
  averageAccuracy: number;
  totalDataProcessed: number;
  lastOptimization: number;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  performanceTrend: {
    accuracy: number[];
    speed: number[];
    timestamps: number[];
  };
  resourceUsage: {
    cpu: number;
    memory: number;
    gpu: number;
  };
  learning_rate: number;
  model_accuracy: number;
  training_time: number;
}

class ContinuousLearningService {
  private models: Map<string, tf.LayersModel> = new Map();
  private performanceMetrics: Map<string, ModelPerformance> = new Map();
  private learningConfig = {
    retrainingThreshold: 0.05, // 5% drop in accuracy
    minDataPoints: 100,
    maxTrainingTime: 300000, // 5 minutes
    performanceWindow: 50, // last 50 predictions
    adaptationInterval: 3600000, // 1 hour
  };
  private isLearning = false;
  private learningQueue: Array<{
    modelId: string;
    data: number[][];
    labels: number[];
    priority: number;
  }> = [];
  private retrainingTriggers: RetrainingTrigger[] = [];

  constructor() {
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    console.log('🔄 Initializing Enhanced Continuous Learning Service...');

    // إعداد النماذج الأساسية
    await this.initializeModels();

    // بدء مراقبة الأداء
    this.startPerformanceMonitoring();

    // بدء معالجة طابور التعلم
    this.startLearningQueue();
  }

  // إعداد النماذج الأساسية
  private async initializeModels(): Promise<void> {
    try {
      // نموذج التنبؤ بالأسعار
      const priceModel = await this.createPriceModel();
      this.models.set('price_predictor', priceModel);

      // نموذج تحليل المشاعر
      const sentimentModel = await this.createSentimentModel();
      this.models.set('sentiment_analyzer', sentimentModel);

      // نموذج كشف الأنماط
      const patternModel = await this.createPatternModel();
      this.models.set('pattern_detector', patternModel);

      // إعداد مقاييس الأداء الأولية
      this.initializePerformanceMetrics();

      console.log('✅ Enhanced models initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing enhanced models:', error);
    }
  }

  // إنشاء نموذج التنبؤ بالأسعار
  private async createPriceModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [10], units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 16, activation: 'relu' }),
        tf.layers.dense({ units: 1, activation: 'sigmoid' })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  // إنشاء نموذج تحليل المشاعر
  private async createSentimentModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [50], units: 128, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({ units: 64, activation: 'relu' }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dense({ units: 3, activation: 'softmax' }) // positive, negative, neutral
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.0005),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  // إنشاء نموذج كشف الأنماط
  private async createPatternModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [20], units: 100, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.25 }),
        tf.layers.dense({ units: 50, activation: 'relu' }),
        tf.layers.dense({ units: 25, activation: 'relu' }),
        tf.layers.dense({ units: 5, activation: 'softmax' }) // 5 pattern types
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'categoricalCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  // إعداد مقاييس الأداء الأولية
  private initializePerformanceMetrics(): void {
    const modelNames = ['price_predictor', 'sentiment_analyzer', 'pattern_detector'];

    modelNames.forEach(modelName => {
      this.performanceMetrics.set(modelName, {
        modelId: modelName,
        modelName: modelName.replace('_', ' ').toUpperCase(),
        accuracy: 0.85 + Math.random() * 0.1, // Initial accuracy
        precision: 0.82 + Math.random() * 0.1,
        recall: 0.80 + Math.random() * 0.1,
        f1Score: 0.81 + Math.random() * 0.1,
        loss: 0.15 + Math.random() * 0.1,
        lastUpdated: Date.now(),
        trainingEpochs: 0,
        dataPoints: 0,
        performanceHistory: []
      });
    });
  }

  // مراقبة الأداء المستمر
  private startPerformanceMonitoring(): void {
    setInterval(() => {
      this.monitorModelPerformance();
    }, 30000); // كل 30 ثانية
  }

  // مراقبة أداء النماذج
  private async monitorModelPerformance(): Promise<void> {
    console.log('📊 Monitoring enhanced model performance...');

    for (const [modelId, metrics] of this.performanceMetrics.entries()) {
      // محاكاة تغييرات الأداء
      const performanceChange = (Math.random() - 0.5) * 0.02; // ±1%
      const newAccuracy = Math.max(0.5, Math.min(0.99, metrics.accuracy + performanceChange));
      const newLoss = Math.max(0.01, Math.min(0.5, metrics.loss - performanceChange));

      // تحديث المقاييس
      metrics.accuracy = newAccuracy;
      metrics.loss = newLoss;
      metrics.precision = Math.max(0.5, Math.min(0.99, metrics.precision + performanceChange * 0.8));
      metrics.recall = Math.max(0.5, Math.min(0.99, metrics.recall + performanceChange * 0.9));
      metrics.f1Score = (2 * metrics.precision * metrics.recall) / (metrics.precision + metrics.recall);

      // إضافة إلى التاريخ
      metrics.performanceHistory.push({
        timestamp: Date.now(),
        accuracy: newAccuracy,
        loss: newLoss,
        epoch: metrics.trainingEpochs
      });

      // الاحتفاظ بآخر 100 نقطة فقط
      if (metrics.performanceHistory.length > 100) {
        metrics.performanceHistory = metrics.performanceHistory.slice(-100);
      }

      // فحص الحاجة لإعادة التدريب
      await this.checkRetrainingTriggers(modelId, metrics);
    }
  }

  async getContinuousLearningMetrics(): Promise<ContinuousLearningMetrics> {
    const allMetrics = Array.from(this.performanceMetrics.values());
    const avgAccuracy = allMetrics.reduce((sum, m) => sum + m.accuracy, 0) / allMetrics.length;
    const totalDataProcessed = allMetrics.reduce((sum, m) => sum + m.dataPoints, 0);

    return {
      totalModels: this.models.size,
      activeModels: this.performanceMetrics.size,
      averageAccuracy: avgAccuracy,
      totalDataProcessed,
      lastOptimization: Date.now() - 1800000, // 30 دقيقة مضت
      systemHealth: avgAccuracy > 0.9 ? 'excellent' : avgAccuracy > 0.8 ? 'good' : avgAccuracy > 0.7 ? 'warning' : 'critical',
      performanceTrend: {
        accuracy: allMetrics.length > 0 ? allMetrics[0].performanceHistory.slice(-5).map(h => h.accuracy) : [0.85, 0.87, 0.89, 0.892, 0.894],
        speed: [120, 115, 118, 122, 125],
        timestamps: [
          Date.now() - 4 * 60 * 60 * 1000,
          Date.now() - 3 * 60 * 60 * 1000,
          Date.now() - 2 * 60 * 60 * 1000,
          Date.now() - 1 * 60 * 60 * 1000,
          Date.now()
        ]
      },
      resourceUsage: {
        cpu: 68 + Math.floor(Math.random() * 20),
        memory: 72 + Math.floor(Math.random() * 15),
        gpu: 45 + Math.floor(Math.random() * 25)
      },
      learning_rate: 0.001,
      model_accuracy: avgAccuracy,
      training_time: 145.7 + Math.random() * 50
    };
  }

  async getAllOnlineLearningStates(): Promise<OnlineLearningState[]> {
    return [
      {
        modelName: 'LSTM_EUR_USD',
        accuracy: 0.894,
        dataProcessed: 125000,
        lastUpdate: Date.now() - 300000,
        driftDetected: false,
        performanceTrend: 'improving',
        adaptationRate: 0.85,
        memoryUsage: 245
      },
      {
        modelName: 'RandomForest_GBP_USD',
        accuracy: 0.876,
        dataProcessed: 98000,
        lastUpdate: Date.now() - 450000,
        driftDetected: true,
        performanceTrend: 'declining',
        adaptationRate: 0.92,
        memoryUsage: 180
      },
      {
        modelName: 'XGBoost_BTC_USD',
        accuracy: 0.912,
        dataProcessed: 156000,
        lastUpdate: Date.now() - 150000,
        driftDetected: false,
        performanceTrend: 'stable',
        adaptationRate: 0.78,
        memoryUsage: 320
      }
    ];
  }

  async runAutoML(dataset: string, targetVariable: string): Promise<AutoMLResult> {
    console.log(`🤖 تشغيل AutoML للبيانات: ${dataset}`);
    
    // محاكاة تشغيل AutoML
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return {
      bestModel: 'XGBoost_Optimized',
      accuracy: 0.934,
      precision: 0.928,
      recall: 0.941,
      f1Score: 0.934,
      trainingTime: 1847,
      modelParams: {
        n_estimators: 150,
        max_depth: 8,
        learning_rate: 0.1,
        subsample: 0.8,
        colsample_bytree: 0.9
      },
      validationMetrics: {
        crossValidationScore: 0.927,
        overfittingRisk: 0.12,
        generalizationScore: 0.891
      },
      recommendations: [
        'النموذج يظهر أداءً ممتازاً على البيانات التجريبية',
        'يُنصح بزيادة عدد الأشجار لتحسين الدقة',
        'معدل التعلم الحالي مثالي لهذا النوع من البيانات'
      ]
    };
  }

  async selectOptimalModel(criteria: any): Promise<ModelSelectionResult> {
    console.log('🎯 اختيار النموذج الأمثل...');
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    return {
      selectedModel: 'Ensemble_LSTM_XGBoost',
      confidence: 0.94,
      performanceScore: 0.931,
      reasons: [
        'أفضل أداء على البيانات التاريخية',
        'مقاومة عالية للتشويش',
        'سرعة تنفيذ مقبولة'
      ],
      alternatives: [
        {
          model: 'Pure_LSTM',
          score: 0.887,
          pros: ['سرعة عالية', 'ذاكرة أقل'],
          cons: ['دقة أقل', 'حساس للتشويش']
        },
        {
          model: 'Pure_XGBoost',
          score: 0.901,
          pros: ['دقة جيدة', 'مقاوم للإفراط في التدريب'],
          cons: ['أبطأ في التنفيذ', 'يحتاج ضبط دقيق']
        }
      ],
      selectionCriteria: {
        accuracy: 0.95,
        speed: 0.8,
        memoryEfficiency: 0.75,
        robustness: 0.9
      }
    };
  }

  async optimizeHyperparameters(modelType: string, searchSpace: any): Promise<HyperparameterOptimization> {
    console.log(`⚙️ تحسين المعاملات للنموذج: ${modelType}`);
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    return {
      algorithm: 'bayesian',
      bestParams: {
        learning_rate: 0.08,
        n_estimators: 175,
        max_depth: 9,
        subsample: 0.85,
        colsample_bytree: 0.92,
        reg_alpha: 0.1,
        reg_lambda: 0.05
      },
      bestScore: 0.946,
      iterations: 150,
      timeElapsed: 2847,
      improvementHistory: [
        { iteration: 1, score: 0.856, params: { learning_rate: 0.1, n_estimators: 100 } },
        { iteration: 25, score: 0.891, params: { learning_rate: 0.09, n_estimators: 125 } },
        { iteration: 50, score: 0.923, params: { learning_rate: 0.085, n_estimators: 150 } },
        { iteration: 100, score: 0.941, params: { learning_rate: 0.08, n_estimators: 170 } },
        { iteration: 150, score: 0.946, params: { learning_rate: 0.08, n_estimators: 175 } }
      ],
      convergenceStatus: 'converged'
    };
  }

  // فحص محفزات إعادة التدريب
  private async checkRetrainingTriggers(
    modelId: string,
    metrics: ModelPerformance
  ): Promise<void> {
    const triggers: RetrainingTrigger[] = [];

    // فحص انخفاض الأداء
    if (metrics.performanceHistory.length >= 10) {
      const recentAccuracy = metrics.performanceHistory.slice(-5).reduce((sum, h) => sum + h.accuracy, 0) / 5;
      const olderAccuracy = metrics.performanceHistory.slice(-10, -5).reduce((sum, h) => sum + h.accuracy, 0) / 5;

      if (olderAccuracy - recentAccuracy > this.learningConfig.retrainingThreshold) {
        const trigger: RetrainingTrigger = {
          triggerId: `perf_drop_${Date.now()}`,
          type: 'performance_drop',
          severity: 'high',
          description: `انخفاض الأداء بنسبة ${((olderAccuracy - recentAccuracy) * 100).toFixed(2)}%`,
          timestamp: Date.now(),
          threshold: this.learningConfig.retrainingThreshold,
          currentValue: olderAccuracy - recentAccuracy,
          actionTaken: false
        };
        triggers.push(trigger);
        this.retrainingTriggers.push(trigger);
      }
    }

    // تنفيذ إعادة التدريب إذا لزم الأمر
    for (const trigger of triggers) {
      if (trigger.severity === 'high' || trigger.severity === 'critical') {
        await this.triggerRetraining(modelId, trigger);
      }
    }
  }

  // تشغيل إعادة التدريب
  private async triggerRetraining(
    modelId: string,
    trigger: RetrainingTrigger
  ): Promise<void> {
    console.log(`🔄 Triggering enhanced retraining for ${modelId}:`, trigger.description);

    // إضافة إلى طابور التعلم
    const mockData = this.generateMockTrainingData(modelId);
    this.learningQueue.push({
      modelId,
      data: mockData.features,
      labels: mockData.labels,
      priority: trigger.severity === 'critical' ? 1 : trigger.severity === 'high' ? 2 : 3
    });

    trigger.actionTaken = true;
  }

  // توليد بيانات تدريب وهمية
  private generateMockTrainingData(modelId: string): {
    features: number[][];
    labels: number[];
  } {
    const dataSize = 200 + Math.floor(Math.random() * 300);
    const features: number[][] = [];
    const labels: number[] = [];

    for (let i = 0; i < dataSize; i++) {
      switch (modelId) {
        case 'price_predictor':
          features.push(Array.from({ length: 10 }, () => Math.random()));
          labels.push(Math.random() > 0.5 ? 1 : 0);
          break;
        case 'sentiment_analyzer':
          features.push(Array.from({ length: 50 }, () => Math.random()));
          labels.push(Math.floor(Math.random() * 3));
          break;
        case 'pattern_detector':
          features.push(Array.from({ length: 20 }, () => Math.random()));
          labels.push(Math.floor(Math.random() * 5));
          break;
      }
    }

    return { features, labels };
  }

  // بدء معالجة طابور التعلم
  private startLearningQueue(): void {
    setInterval(() => {
      this.processLearningQueue();
    }, 60000); // كل دقيقة
  }

  // معالجة طابور التعلم
  private async processLearningQueue(): Promise<void> {
    if (this.isLearning || this.learningQueue.length === 0) return;

    // ترتيب حسب الأولوية
    this.learningQueue.sort((a, b) => a.priority - b.priority);

    const task = this.learningQueue.shift();
    if (!task) return;

    this.isLearning = true;
    console.log(`🎓 Starting enhanced retraining for ${task.modelId}...`);

    try {
      await this.retrainModel(task.modelId, task.data, task.labels);
      console.log(`✅ Enhanced retraining completed for ${task.modelId}`);
    } catch (error) {
      console.error(`❌ Enhanced retraining failed for ${task.modelId}:`, error);
    } finally {
      this.isLearning = false;
    }
  }

  // إعادة تدريب النموذج
  private async retrainModel(
    modelId: string,
    data: number[][],
    labels: number[]
  ): Promise<void> {
    const model = this.models.get(modelId);
    if (!model) throw new Error(`Model ${modelId} not found`);

    const metrics = this.performanceMetrics.get(modelId);
    if (!metrics) throw new Error(`Metrics for ${modelId} not found`);

    // تحضير البيانات
    const xs = tf.tensor2d(data);
    let ys: tf.Tensor;

    if (modelId === 'price_predictor') {
      ys = tf.tensor2d(labels.map(l => [l]));
    } else {
      // تحويل إلى one-hot encoding للنماذج الأخرى
      const numClasses = modelId === 'sentiment_analyzer' ? 3 : 5;
      ys = tf.oneHot(tf.tensor1d(labels, 'int32'), numClasses);
    }

    try {
      // التدريب التدريجي
      const history = await model.fit(xs, ys, {
        epochs: 10,
        batchSize: 32,
        validationSplit: 0.2,
        verbose: 0,
        callbacks: {
          onEpochEnd: (epoch, logs) => {
            if (logs) {
              metrics.performanceHistory.push({
                timestamp: Date.now(),
                accuracy: logs.acc || logs.val_acc || metrics.accuracy,
                loss: logs.loss || logs.val_loss || metrics.loss,
                epoch: metrics.trainingEpochs + epoch + 1
              });
            }
          }
        }
      });

      // تحديث المقاييس
      const finalLogs = history.history;
      if (finalLogs.acc && finalLogs.acc.length > 0) {
        metrics.accuracy = finalLogs.acc[finalLogs.acc.length - 1] as number;
      }
      if (finalLogs.loss && finalLogs.loss.length > 0) {
        metrics.loss = finalLogs.loss[finalLogs.loss.length - 1] as number;
      }

      metrics.trainingEpochs += 10;
      metrics.dataPoints += data.length;
      metrics.lastUpdated = Date.now();

      console.log(`📈 Enhanced model ${modelId} retrained successfully. New accuracy: ${(metrics.accuracy * 100).toFixed(2)}%`);

    } finally {
      xs.dispose();
      ys.dispose();
    }
  }

  // الحصول على مقاييس الأداء
  getPerformanceMetrics(modelId?: string): ModelPerformance[] {
    if (modelId) {
      const metrics = this.performanceMetrics.get(modelId);
      return metrics ? [metrics] : [];
    }
    return Array.from(this.performanceMetrics.values());
  }

  // الحصول على مقاييس التعلم
  getLearningMetrics(modelId: string): LearningMetrics {
    const metrics = this.performanceMetrics.get(modelId);
    if (!metrics) {
      throw new Error(`Model ${modelId} not found`);
    }

    // حساب معدل التقارب
    const convergenceRate = this.calculateConvergenceRate(metrics.performanceHistory);

    // حساب درجة الاستقرار
    const stabilityScore = this.calculateStabilityScore(metrics.performanceHistory);

    // حساب درجة القدرة على التكيف
    const adaptabilityScore = this.calculateAdaptabilityScore(metrics);

    // تقييم خطر الإفراط في التدريب
    const overfittingRisk = this.assessOverfittingRisk(metrics);

    return {
      learningRate: 0.001,
      convergenceRate,
      stabilityScore,
      adaptabilityScore,
      overfittingRisk,
      recommendedAction: this.getRecommendedAction(metrics, overfittingRisk, stabilityScore)
    };
  }

  // حساب معدل التقارب
  private calculateConvergenceRate(history: ModelPerformance['performanceHistory']): number {
    if (history.length < 10) return 0.5;

    const recent = history.slice(-10);
    const variance = this.calculateVariance(recent.map(h => h.accuracy));
    return Math.max(0, 1 - variance * 10); // كلما قل التباين، زاد التقارب
  }

  // حساب درجة الاستقرار
  private calculateStabilityScore(history: ModelPerformance['performanceHistory']): number {
    if (history.length < 5) return 0.5;

    const recent = history.slice(-20);
    const accuracyTrend = this.calculateTrend(recent.map(h => h.accuracy));
    return Math.max(0, Math.min(1, 0.5 + accuracyTrend));
  }

  // حساب درجة القدرة على التكيف
  private calculateAdaptabilityScore(metrics: ModelPerformance): number {
    const recentTraining = Date.now() - metrics.lastUpdated;
    const daysSinceUpdate = recentTraining / (1000 * 60 * 60 * 24);

    // كلما كان التحديث أحدث، زادت القدرة على التكيف
    return Math.max(0, Math.min(1, 1 - daysSinceUpdate / 7));
  }

  // تقييم خطر الإفراط في التدريب
  private assessOverfittingRisk(metrics: ModelPerformance): number {
    if (metrics.performanceHistory.length < 10) return 0.3;

    const recent = metrics.performanceHistory.slice(-10);
    const accuracyVariance = this.calculateVariance(recent.map(h => h.accuracy));
    const lossVariance = this.calculateVariance(recent.map(h => h.loss));

    // إذا كان هناك تباين عالي، فقد يكون هناك إفراط في التدريب
    return Math.min(1, (accuracyVariance + lossVariance) * 5);
  }

  // الحصول على الإجراء الموصى به
  private getRecommendedAction(
    metrics: ModelPerformance,
    overfittingRisk: number,
    stabilityScore: number
  ): LearningMetrics['recommendedAction'] {
    if (overfittingRisk > 0.7) return 'adjust_params';
    if (metrics.accuracy < 0.7) return 'retrain';
    if (stabilityScore < 0.5) return 'collect_data';
    return 'continue';
  }

  // حساب التباين
  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }

  // حساب الاتجاه
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    let trend = 0;
    for (let i = 1; i < values.length; i++) {
      trend += values[i] - values[i - 1];
    }
    return trend / (values.length - 1);
  }

  // الحصول على حالة التعلم
  getLearningStatus(): {
    isActive: boolean;
    queueSize: number;
    currentTask: string | null;
    totalModels: number;
    activeModels: number;
    recentTriggers: RetrainingTrigger[];
  } {
    return {
      isActive: this.isLearning,
      queueSize: this.learningQueue.length,
      currentTask: this.isLearning ? this.learningQueue[0]?.modelId || null : null,
      totalModels: this.models.size,
      activeModels: this.performanceMetrics.size,
      recentTriggers: this.retrainingTriggers.slice(-10) // آخر 10 محفزات
    };
  }

  // تحديث إعدادات التعلم
  updateLearningConfig(config: Partial<typeof this.learningConfig>): void {
    this.learningConfig = { ...this.learningConfig, ...config };
    console.log('⚙️ Enhanced learning configuration updated:', this.learningConfig);
  }

  // إجبار إعادة التدريب
  async forceRetraining(modelId: string): Promise<void> {
    const mockData = this.generateMockTrainingData(modelId);
    this.learningQueue.unshift({
      modelId,
      data: mockData.features,
      labels: mockData.labels,
      priority: 1
    });

    console.log(`🔄 Enhanced forced retraining queued for ${modelId}`);
  }

  // التعلم التكيفي المحسن
  async adaptModel(modelName: string, newData: any[]): Promise<void> {
    console.log(`🔄 Enhanced adaptive learning for: ${modelName}`);

    // كشف انحراف البيانات أولاً
    const driftDetected = await this.detectDataDrift(modelName, newData);

    if (driftDetected) {
      console.log(`📊 Data drift detected for ${modelName}, triggering adaptation...`);
      await this.forceRetraining(modelName);
    } else {
      console.log(`✅ No significant drift detected for ${modelName}`);
    }
  }

  // كشف انحراف البيانات المحسن
  async detectDataDrift(modelName: string, newData: any[]): Promise<boolean> {
    console.log(`🔍 Enhanced drift detection for: ${modelName}`);

    // محاكاة كشف الانحراف بناءً على التوزيع الإحصائي
    const driftProbability = Math.random();
    const driftThreshold = 0.15; // 15% احتمال كشف انحراف

    const isDrift = driftProbability < driftThreshold;

    if (isDrift) {
      // إضافة محفز انحراف البيانات
      const trigger: RetrainingTrigger = {
        triggerId: `data_drift_${Date.now()}`,
        type: 'data_drift',
        severity: 'medium',
        description: `تم اكتشاف انحراف في توزيع البيانات لنموذج ${modelName}`,
        timestamp: Date.now(),
        threshold: driftThreshold,
        currentValue: driftProbability,
        actionTaken: false
      };

      this.retrainingTriggers.push(trigger);
    }

    return isDrift;
  }

  // إعادة تدريب تلقائي محسن
  async autoRetrain(modelName: string): Promise<void> {
    console.log(`🔄 Enhanced auto-retraining for: ${modelName}`);

    const trigger: RetrainingTrigger = {
      triggerId: `auto_retrain_${Date.now()}`,
      type: 'scheduled',
      severity: 'medium',
      description: `إعادة تدريب مجدولة لنموذج ${modelName}`,
      timestamp: Date.now(),
      threshold: 0,
      currentValue: 1,
      actionTaken: false
    };

    await this.triggerRetraining(modelName, trigger);
  }
}

export const continuousLearningService = new ContinuousLearningService();
