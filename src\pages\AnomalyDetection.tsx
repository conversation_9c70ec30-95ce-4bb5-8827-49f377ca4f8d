import React, { useState, useEffect } from 'react';
import { AlertTriangle, Activity, TrendingUp, Volume2, Bell, RefreshCw, Eye } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { anomalyDetectionService, AnomalyDetection, AnomalyAlert } from '@/services/anomalyDetectionService';

interface AnomalyDetectionPageProps {
  lang?: 'en' | 'ar';
}

const AnomalyDetectionPage: React.FC<AnomalyDetectionPageProps> = ({ lang = 'ar' }) => {
  const [anomalies, setAnomalies] = useState<AnomalyDetection[]>([]);
  const [alerts, setAlerts] = useState<AnomalyAlert[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAnomalyData();
  }, []);

  const loadAnomalyData = async () => {
    setIsLoading(true);
    try {
      // محاكاة بيانات السوق
      const mockMarketData = Array.from({ length: 50 }, (_, i) => ({
        price: 50000 + Math.random() * 10000 - 5000 + (i % 10 === 0 ? Math.random() * 5000 - 2500 : 0),
        volume: 1000000 + Math.random() * 500000 + (i % 15 === 0 ? Math.random() * 2000000 : 0),
        timestamp: Date.now() - (50 - i) * 3600000,
        symbol: 'BTC'
      }));

      // كشف الأحداث غير المتوقعة
      const detectedAnomalies = await anomalyDetectionService.detectAnomalies(mockMarketData);
      setAnomalies(detectedAnomalies);

      // توليد التنبيهات
      const generatedAlerts = await anomalyDetectionService.generateMarketEventAlerts(detectedAnomalies);
      setAlerts(generatedAlerts);

      // حساب الإحصائيات
      const stats = anomalyDetectionService.getAnomalyStatistics(detectedAnomalies);
      setStatistics(stats);

    } catch (error) {
      console.error('Error loading anomaly data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'low': return 'default';
      case 'medium': return 'secondary';
      case 'high': return 'destructive';
      case 'critical': return 'destructive';
      default: return 'outline';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'price': return <TrendingUp className="h-4 w-4" />;
      case 'volume': return <Volume2 className="h-4 w-4" />;
      case 'pattern': return <Activity className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* العنوان الرئيسي */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <AlertTriangle className="h-8 w-8 text-red-400" />
            {lang === 'ar' ? 'كشف الأحداث غير المتوقعة' : 'Anomaly Detection'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar' 
              ? 'نظام متقدم لكشف الأحداث غير العادية في السوق والتنبيه المبكر للمخاطر'
              : 'Advanced system for detecting unusual market events and early risk alerts'
            }
          </p>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'إجمالي الأحداث' : 'Total Events'}
              </h3>
              <p className="text-2xl font-bold text-red-400">
                {statistics ? statistics.total : anomalies.length}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'أحداث السعر' : 'Price Events'}
              </h3>
              <p className="text-2xl font-bold text-blue-400">
                {statistics ? (statistics.byType.price || 0) : anomalies.filter(a => a.type === 'price').length}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Volume2 className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'أحداث الحجم' : 'Volume Events'}
              </h3>
              <p className="text-2xl font-bold text-green-400">
                {statistics ? (statistics.byType.volume || 0) : anomalies.filter(a => a.type === 'volume').length}
              </p>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Bell className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {lang === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts'}
              </h3>
              <p className="text-2xl font-bold text-yellow-400">
                {alerts.filter(alert => !alert.isRead).length}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-center gap-4">
          <Button onClick={loadAnomalyData} disabled={isLoading} className="flex items-center gap-2">
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? (lang === 'ar' ? 'جاري التحليل...' : 'Analyzing...') : (lang === 'ar' ? 'تحديث البيانات' : 'Refresh Data')}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* قائمة الأحداث المكتشفة */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-blue-400" />
                {lang === 'ar' ? 'الأحداث المكتشفة' : 'Detected Events'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {anomalies.length > 0 ? (
                  anomalies.map((anomaly, index) => (
                    <div key={index} className="p-4 bg-slate-700 rounded-lg border-l-4 border-red-400">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(anomaly.type)}
                          <span className="font-medium text-white">
                            {lang === 'ar' ? anomaly.description : `${anomaly.type.toUpperCase()} Anomaly`}
                          </span>
                        </div>
                        <Badge variant={getSeverityBadge(anomaly.severity)}>
                          {lang === 'ar' ? 
                            (anomaly.severity === 'low' ? 'منخفض' : 
                             anomaly.severity === 'medium' ? 'متوسط' : 
                             anomaly.severity === 'high' ? 'عالي' : 'حرج') 
                            : anomaly.severity}
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">
                            {lang === 'ar' ? 'درجة الشذوذ:' : 'Anomaly Score:'}
                          </span>
                          <span className={getSeverityColor(anomaly.severity)}>
                            {(anomaly.anomalyScore * 100).toFixed(1)}%
                          </span>
                        </div>
                        
                        <Progress 
                          value={anomaly.confidence * 100} 
                          className="h-2"
                        />
                        
                        <div className="text-xs text-gray-400">
                          {lang === 'ar' ? 'الثقة:' : 'Confidence:'} {(anomaly.confidence * 100).toFixed(1)}%
                        </div>
                        
                        <div className="text-xs text-gray-400">
                          {new Date(anomaly.timestamp).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-400">
                      {lang === 'ar' ? 'لا توجد أحداث غير متوقعة حالياً' : 'No anomalies detected currently'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* التنبيهات النشطة */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-yellow-400" />
                {lang === 'ar' ? 'التنبيهات النشطة' : 'Active Alerts'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {alerts.length > 0 ? (
                  alerts.map((alert, index) => (
                    <div key={index} className={`p-4 rounded-lg border-l-4 ${
                      alert.type === 'danger' ? 'bg-red-900/20 border-red-400' : 
                      alert.type === 'warning' ? 'bg-yellow-900/20 border-yellow-400' : 
                      'bg-blue-900/20 border-blue-400'
                    }`}>
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-white">{alert.title}</h4>
                        {alert.actionRequired && (
                          <Badge variant="destructive" className="text-xs">
                            {lang === 'ar' ? 'يتطلب إجراء' : 'Action Required'}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-300 mb-2">{alert.message}</p>
                      
                      <div className="text-xs text-gray-400">
                        {new Date(alert.timestamp).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-400">
                      {lang === 'ar' ? 'لا توجد تنبيهات نشطة' : 'No active alerts'}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* إحصائيات مفصلة */}
        {statistics && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle>
                {lang === 'ar' ? 'إحصائيات مفصلة' : 'Detailed Statistics'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-3">
                    {lang === 'ar' ? 'حسب النوع' : 'By Type'}
                  </h4>
                  <div className="space-y-2">
                    {Object.entries(statistics.byType).map(([type, count]) => (
                      <div key={type} className="flex justify-between">
                        <span className="text-gray-400 capitalize">{type}</span>
                        <span className="text-white">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-white mb-3">
                    {lang === 'ar' ? 'حسب الشدة' : 'By Severity'}
                  </h4>
                  <div className="space-y-2">
                    {Object.entries(statistics.bySeverity).map(([severity, count]) => (
                      <div key={severity} className="flex justify-between">
                        <span className={`capitalize ${getSeverityColor(severity)}`}>{severity}</span>
                        <span className="text-white">{count as number}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-white mb-3">
                    {lang === 'ar' ? 'متوسط النتيجة' : 'Average Score'}
                  </h4>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-400 mb-2">
                      {(statistics.averageScore * 100).toFixed(1)}%
                    </div>
                    <Progress value={statistics.averageScore * 100} className="h-3" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AnomalyDetectionPage;
