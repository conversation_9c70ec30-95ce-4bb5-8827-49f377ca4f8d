import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Package, 
  CheckCircle, 
  BarChart3,
  Brain,
  Database,
  Globe,
  Shield,
  Code,
  Settings,
  Zap
} from 'lucide-react';

interface SimpleLibrariesShowcaseProps {
  lang?: 'en' | 'ar';
}

const SimpleLibrariesShowcase: React.FC<SimpleLibrariesShowcaseProps> = ({ lang = 'en' }) => {
  const [installationProgress] = useState(100);

  const libraries = [
    {
      name: 'Chart.js',
      version: '4.4.0',
      category: 'Charts',
      status: 'Active',
      description: lang === 'ar' ? 'مكتبة رسوم بيانية متقدمة' : 'Advanced charting library',
      icon: BarChart3,
      color: '#FF6384'
    },
    {
      name: 'React-Chartjs-2',
      version: '5.2.0',
      category: 'Charts',
      status: 'Active',
      description: lang === 'ar' ? 'مكون React لـ Chart.js' : 'React wrapper for Chart.js',
      icon: BarChart3,
      color: '#36A2EB'
    },
    {
      name: 'TensorFlow.js',
      version: '4.22.0',
      category: 'AI',
      status: 'Active',
      description: lang === 'ar' ? 'مكتبة التعلم الآلي' : 'Machine learning library',
      icon: Brain,
      color: '#FF6F00'
    },
    {
      name: 'Brain.js',
      version: '2.0.0-beta.23',
      category: 'AI',
      status: 'Active',
      description: lang === 'ar' ? 'شبكات عصبية مبسطة' : 'Simplified neural networks',
      icon: Brain,
      color: '#9C27B0'
    },
    {
      name: 'Dexie.js',
      version: '4.0.4',
      category: 'Data',
      status: 'Active',
      description: lang === 'ar' ? 'قاعدة بيانات محلية' : 'Local database',
      icon: Database,
      color: '#4CAF50'
    },
    {
      name: 'RxJS',
      version: '7.8.1',
      category: 'Data',
      status: 'Active',
      description: lang === 'ar' ? 'البرمجة التفاعلية' : 'Reactive programming',
      icon: Zap,
      color: '#E91E63'
    },
    {
      name: 'i18next',
      version: '23.7.16',
      category: 'UI',
      status: 'Active',
      description: lang === 'ar' ? 'نظام ترجمة متقدم' : 'Advanced internationalization',
      icon: Globe,
      color: '#00BCD4'
    },
    {
      name: 'Technical Indicators',
      version: '3.1.0',
      category: 'Trading',
      status: 'Active',
      description: lang === 'ar' ? 'مؤشرات فنية شاملة' : 'Comprehensive technical indicators',
      icon: BarChart3,
      color: '#607D8B'
    },
    {
      name: 'Speakeasy',
      version: '2.0.0',
      category: 'Security',
      status: 'Active',
      description: lang === 'ar' ? 'مصادقة ثنائية' : 'Two-factor authentication',
      icon: Shield,
      color: '#F44336'
    },
    {
      name: 'Winston',
      version: '3.17.0',
      category: 'Utils',
      status: 'Active',
      description: lang === 'ar' ? 'نظام سجلات متقدم' : 'Advanced logging system',
      icon: Code,
      color: '#673AB7'
    }
  ];

  const categoryStats = {
    Charts: libraries.filter(lib => lib.category === 'Charts').length,
    AI: libraries.filter(lib => lib.category === 'AI').length,
    Data: libraries.filter(lib => lib.category === 'Data').length,
    UI: libraries.filter(lib => lib.category === 'UI').length,
    Trading: libraries.filter(lib => lib.category === 'Trading').length,
    Security: libraries.filter(lib => lib.category === 'Security').length,
    Utils: libraries.filter(lib => lib.category === 'Utils').length
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Charts': return 'bg-blue-500';
      case 'AI': return 'bg-purple-500';
      case 'Data': return 'bg-green-500';
      case 'UI': return 'bg-orange-500';
      case 'Trading': return 'bg-cyan-500';
      case 'Security': return 'bg-red-500';
      case 'Utils': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* العنوان الرئيسي */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {lang === 'ar' ? 'عرض المكتبات المضافة' : 'Libraries Showcase'}
          </h1>
          <p className="text-gray-400">
            {lang === 'ar' 
              ? 'جميع المكتبات والتقنيات المتقدمة المضافة للمشروع'
              : 'All advanced libraries and technologies added to the project'
            }
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            {libraries.length} {lang === 'ar' ? 'مكتبة نشطة' : 'Active Libraries'}
          </Badge>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        {Object.entries(categoryStats).map(([category, count]) => (
          <Card key={category} className="bg-slate-800 border-slate-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-400">{category}</p>
                  <p className="text-xl font-bold text-white">{count}</p>
                </div>
                <div className={`p-2 rounded-lg ${getCategoryColor(category)}`}>
                  <Package className="h-4 w-4 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* تقدم التثبيت */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="h-5 w-5" />
            {lang === 'ar' ? 'حالة التثبيت' : 'Installation Status'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-300">
                {lang === 'ar' ? 'تقدم التثبيت' : 'Installation Progress'}
              </span>
              <span className="text-white">{installationProgress}%</span>
            </div>
            <Progress value={installationProgress} className="h-2" />
            <p className="text-xs text-gray-400">
              {lang === 'ar' ? 'تم تثبيت جميع المكتبات بنجاح' : 'All libraries installed successfully'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المكتبات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {libraries.map((library, index) => {
          const Icon = library.icon;
          return (
            <Card key={index} className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Icon className="h-5 w-5" style={{ color: library.color }} />
                  {library.name}
                  <Badge variant="outline" className="ml-auto">
                    v{library.version}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-gray-400">{library.description}</p>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="default" className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      {library.status}
                    </Badge>
                    <Badge variant="outline">
                      {library.category}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* ملخص الميزات */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">
            {lang === 'ar' ? 'الميزات المضافة' : 'Added Features'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-white">
                {lang === 'ar' ? 'الرسوم البيانية المتقدمة' : 'Advanced Charts'}
              </h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>• {lang === 'ar' ? 'رسوم الشموع اليابانية' : 'Candlestick Charts'}</li>
                <li>• {lang === 'ar' ? '89+ مؤشر فني' : '89+ Technical Indicators'}</li>
                <li>• {lang === 'ar' ? 'كشف الأنماط التلقائي' : 'Automatic Pattern Detection'}</li>
                <li>• {lang === 'ar' ? 'تصدير الرسوم البيانية' : 'Chart Export'}</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-white">
                {lang === 'ar' ? 'الذكاء الاصطناعي' : 'Artificial Intelligence'}
              </h4>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>• {lang === 'ar' ? 'شبكات عصبية متقدمة' : 'Advanced Neural Networks'}</li>
                <li>• {lang === 'ar' ? 'تحليل المشاعر' : 'Sentiment Analysis'}</li>
                <li>• {lang === 'ar' ? 'التنبؤ بالأسعار' : 'Price Prediction'}</li>
                <li>• {lang === 'ar' ? 'تحسين الاستراتيجيات' : 'Strategy Optimization'}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleLibrariesShowcase;
