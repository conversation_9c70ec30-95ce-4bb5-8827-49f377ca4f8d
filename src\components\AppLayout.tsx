import React from 'react';
import { Outlet } from 'react-router-dom';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { LanguageSwitcher } from '@/components/LanguageSwitcher';

interface AppLayoutProps {
  lang: 'en' | 'ar';
  setLang: (lang: 'en' | 'ar') => void;
  children?: React.ReactNode;
}

export function AppLayout({ lang, setLang, children }: AppLayoutProps) {
  console.log('AppLayout rendering, lang:', lang);

  return (
    <div 
      dir={lang === 'ar' ? 'rtl' : 'ltr'} 
      className={`flex flex-col min-h-screen bg-slate-900 text-white w-full transition-all duration-300 ${
        lang === 'ar' ? 'font-arabic' : 'font-english'
      }`}
    >
      <header className="bg-slate-800 border-b border-gray-700 p-4 shadow-lg">
        <div className={`flex items-center justify-between w-full ${
          lang === 'ar' ? 'flex-row-reverse' : 'flex-row'
        }`}>
          <div className={`flex items-center gap-4 ${
            lang === 'ar' ? 'flex-row-reverse' : 'flex-row'
          }`}>
            <SidebarTrigger className="text-white hover:bg-slate-700 transition-colors" />
            <h1 className={`text-xl font-bold text-white whitespace-nowrap ${
              lang === 'ar' ? 'text-right' : 'text-left'
            }`}>
              {lang === 'ar' ? 'منصة التداول الذكي' : 'AI Trading Platform'}
            </h1>
          </div>
          <div className="flex-shrink-0">
            <LanguageSwitcher currentLang={lang} setLang={setLang} />
          </div>
        </div>
      </header>
      
      <main className="flex-1 p-6 bg-slate-900 overflow-auto">
        {children || <Outlet context={{ lang }} />}
      </main>
    </div>
  );
}
