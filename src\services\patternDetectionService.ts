// خدمة كشف الأنماط التلقائي المتقدمة
import { CandlestickData } from './enhancedMarketDataService';

export interface DetectedPattern {
  id: string;
  name: string;
  nameAr: string;
  type: 'bullish' | 'bearish' | 'neutral' | 'reversal' | 'continuation';
  confidence: number; // 0-100
  startIndex: number;
  endIndex: number;
  startTime: number;
  endTime: number;
  description: string;
  descriptionAr: string;
  significance: 'low' | 'medium' | 'high' | 'critical';
  priceTarget?: number;
  stopLoss?: number;
  probability: number; // احتمالية نجاح النمط
  volume?: number; // متوسط الحجم خلال النمط
  coordinates: Array<{ x: number; y: number }>; // إحداثيات الرسم
  patternData: {
    bodySize?: number;
    wickRatio?: number;
    priceRange?: number;
    volumeConfirmation?: boolean;
    trendContext?: 'uptrend' | 'downtrend' | 'sideways';
  };
}

export interface PatternStatistics {
  totalPatterns: number;
  bullishPatterns: number;
  bearishPatterns: number;
  neutralPatterns: number;
  highConfidencePatterns: number;
  patternsByType: Record<string, number>;
  averageConfidence: number;
  successRate: number;
  mostFrequentPattern: string;
}

class PatternDetectionService {
  private patterns: DetectedPattern[] = [];
  private patternDefinitions: Map<string, any> = new Map();

  constructor() {
    this.initializePatternDefinitions();
    console.log('🔍 Pattern Detection Service initialized');
  }

  // تهيئة تعريفات الأنماط
  private initializePatternDefinitions(): void {
    // تعريفات الأنماط مع معايير الكشف
    this.patternDefinitions.set('doji', {
      name: 'Doji',
      nameAr: 'دوجي',
      type: 'neutral',
      minBodyRatio: 0.0,
      maxBodyRatio: 0.1,
      minWickRatio: 0.3,
      significance: 'high',
      description: 'Indecision pattern indicating potential reversal',
      descriptionAr: 'نمط تردد يشير إلى انعكاس محتمل'
    });

    this.patternDefinitions.set('hammer', {
      name: 'Hammer',
      nameAr: 'المطرقة',
      type: 'bullish',
      minLowerWickRatio: 2.0,
      maxUpperWickRatio: 0.5,
      maxBodyRatio: 0.3,
      significance: 'high',
      description: 'Bullish reversal pattern at support levels',
      descriptionAr: 'نمط انعكاس صعودي عند مستويات الدعم'
    });

    this.patternDefinitions.set('shooting_star', {
      name: 'Shooting Star',
      nameAr: 'النجم الساقط',
      type: 'bearish',
      minUpperWickRatio: 2.0,
      maxLowerWickRatio: 0.5,
      maxBodyRatio: 0.3,
      significance: 'high',
      description: 'Bearish reversal pattern at resistance levels',
      descriptionAr: 'نمط انعكاس هبوطي عند مستويات المقاومة'
    });

    this.patternDefinitions.set('engulfing_bullish', {
      name: 'Bullish Engulfing',
      nameAr: 'الابتلاع الصعودي',
      type: 'bullish',
      significance: 'critical',
      description: 'Strong bullish reversal pattern',
      descriptionAr: 'نمط انعكاس صعودي قوي'
    });

    this.patternDefinitions.set('engulfing_bearish', {
      name: 'Bearish Engulfing',
      nameAr: 'الابتلاع الهبوطي',
      type: 'bearish',
      significance: 'critical',
      description: 'Strong bearish reversal pattern',
      descriptionAr: 'نمط انعكاس هبوطي قوي'
    });

    this.patternDefinitions.set('morning_star', {
      name: 'Morning Star',
      nameAr: 'نجمة الصباح',
      type: 'bullish',
      significance: 'critical',
      description: 'Three-candle bullish reversal pattern',
      descriptionAr: 'نمط انعكاس صعودي من ثلاث شموع'
    });

    this.patternDefinitions.set('evening_star', {
      name: 'Evening Star',
      nameAr: 'نجمة المساء',
      type: 'bearish',
      significance: 'critical',
      description: 'Three-candle bearish reversal pattern',
      descriptionAr: 'نمط انعكاس هبوطي من ثلاث شموع'
    });

    this.patternDefinitions.set('head_shoulders', {
      name: 'Head and Shoulders',
      nameAr: 'الرأس والكتفين',
      type: 'bearish',
      significance: 'critical',
      description: 'Major bearish reversal pattern',
      descriptionAr: 'نمط انعكاس هبوطي رئيسي'
    });

    this.patternDefinitions.set('inverse_head_shoulders', {
      name: 'Inverse Head and Shoulders',
      nameAr: 'الرأس والكتفين المقلوب',
      type: 'bullish',
      significance: 'critical',
      description: 'Major bullish reversal pattern',
      descriptionAr: 'نمط انعكاس صعودي رئيسي'
    });

    this.patternDefinitions.set('double_top', {
      name: 'Double Top',
      nameAr: 'القمة المزدوجة',
      type: 'bearish',
      significance: 'high',
      description: 'Bearish reversal pattern with two peaks',
      descriptionAr: 'نمط انعكاس هبوطي بقمتين'
    });

    this.patternDefinitions.set('double_bottom', {
      name: 'Double Bottom',
      nameAr: 'القاع المزدوج',
      type: 'bullish',
      significance: 'high',
      description: 'Bullish reversal pattern with two troughs',
      descriptionAr: 'نمط انعكاس صعودي بقاعين'
    });

    this.patternDefinitions.set('triangle_ascending', {
      name: 'Ascending Triangle',
      nameAr: 'المثلث الصاعد',
      type: 'bullish',
      significance: 'medium',
      description: 'Bullish continuation pattern',
      descriptionAr: 'نمط استمرار صعودي'
    });

    this.patternDefinitions.set('triangle_descending', {
      name: 'Descending Triangle',
      nameAr: 'المثلث الهابط',
      type: 'bearish',
      significance: 'medium',
      description: 'Bearish continuation pattern',
      descriptionAr: 'نمط استمرار هبوطي'
    });

    this.patternDefinitions.set('flag_bullish', {
      name: 'Bullish Flag',
      nameAr: 'العلم الصعودي',
      type: 'bullish',
      significance: 'medium',
      description: 'Bullish continuation pattern after strong move',
      descriptionAr: 'نمط استمرار صعودي بعد حركة قوية'
    });

    this.patternDefinitions.set('flag_bearish', {
      name: 'Bearish Flag',
      nameAr: 'العلم الهبوطي',
      type: 'bearish',
      significance: 'medium',
      description: 'Bearish continuation pattern after strong move',
      descriptionAr: 'نمط استمرار هبوطي بعد حركة قوية'
    });
  }

  // كشف جميع الأنماط في البيانات
  async detectAllPatterns(data: CandlestickData[]): Promise<DetectedPattern[]> {
    if (!data || data.length < 3) {
      return [];
    }

    this.patterns = [];

    console.log(`🔍 Starting pattern detection on ${data.length} candles`);

    // كشف الأنماط المختلفة
    await this.detectSingleCandlePatterns(data);
    await this.detectTwoCandlePatterns(data);
    await this.detectThreeCandlePatterns(data);
    await this.detectComplexPatterns(data);

    // ترتيب الأنماط حسب الأهمية والثقة
    this.patterns.sort((a, b) => {
      const significanceOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aScore = significanceOrder[a.significance] * a.confidence;
      const bScore = significanceOrder[b.significance] * b.confidence;
      return bScore - aScore;
    });

    console.log(`✅ Detected ${this.patterns.length} patterns`);
    return this.patterns;
  }

  // كشف أنماط الشمعة الواحدة
  private async detectSingleCandlePatterns(data: CandlestickData[]): Promise<void> {
    for (let i = 0; i < data.length; i++) {
      const candle = data[i];

      // حساب النسب
      const bodySize = Math.abs(candle.close - candle.open);
      const totalRange = candle.high - candle.low;
      const upperWick = candle.high - Math.max(candle.open, candle.close);
      const lowerWick = Math.min(candle.open, candle.close) - candle.low;

      if (totalRange === 0) continue;

      const bodyRatio = bodySize / totalRange;
      const upperWickRatio = upperWick / bodySize || 0;
      const lowerWickRatio = lowerWick / bodySize || 0;

      // كشف Doji
      if (bodyRatio <= 0.1 && totalRange > 0) {
        const confidence = Math.max(70, 100 - (bodyRatio * 1000));
        this.addPattern('doji', i, i, data, confidence, {
          bodySize: bodyRatio,
          wickRatio: (upperWick + lowerWick) / totalRange
        });
      }

      // كشف Hammer
      if (lowerWickRatio >= 2.0 && upperWickRatio <= 0.5 && bodyRatio <= 0.3) {
        const confidence = Math.min(95, 60 + (lowerWickRatio * 10));
        this.addPattern('hammer', i, i, data, confidence, {
          bodySize: bodyRatio,
          wickRatio: lowerWickRatio
        });
      }

      // كشف Shooting Star
      if (upperWickRatio >= 2.0 && lowerWickRatio <= 0.5 && bodyRatio <= 0.3) {
        const confidence = Math.min(95, 60 + (upperWickRatio * 10));
        this.addPattern('shooting_star', i, i, data, confidence, {
          bodySize: bodyRatio,
          wickRatio: upperWickRatio
        });
      }
    }
  }

  // كشف أنماط الشمعتين
  private async detectTwoCandlePatterns(data: CandlestickData[]): Promise<void> {
    for (let i = 1; i < data.length; i++) {
      const prev = data[i - 1];
      const curr = data[i];

      // كشف Bullish Engulfing
      if (prev.close < prev.open && curr.close > curr.open &&
          curr.open < prev.close && curr.close > prev.open) {
        const engulfingRatio = (curr.close - curr.open) / (prev.open - prev.close);
        const confidence = Math.min(95, 70 + (engulfingRatio * 15));
        this.addPattern('engulfing_bullish', i - 1, i, data, confidence, {
          priceRange: engulfingRatio
        });
      }

      // كشف Bearish Engulfing
      if (prev.close > prev.open && curr.close < curr.open &&
          curr.open > prev.close && curr.close < prev.open) {
        const engulfingRatio = (curr.open - curr.close) / (prev.close - prev.open);
        const confidence = Math.min(95, 70 + (engulfingRatio * 15));
        this.addPattern('engulfing_bearish', i - 1, i, data, confidence, {
          priceRange: engulfingRatio
        });
      }
    }
  }

  // كشف أنماط الثلاث شموع
  private async detectThreeCandlePatterns(data: CandlestickData[]): Promise<void> {
    for (let i = 2; i < data.length; i++) {
      const first = data[i - 2];
      const middle = data[i - 1];
      const last = data[i];

      // كشف Morning Star
      if (this.isBearishCandle(first) && this.isSmallBody(middle) && this.isBullishCandle(last)) {
        if (middle.high < first.close && last.open > middle.high && last.close > (first.open + first.close) / 2) {
          const confidence = this.calculateThreeCandleConfidence(first, middle, last, 'bullish');
          this.addPattern('morning_star', i - 2, i, data, confidence, {
            priceRange: (last.close - first.close) / first.close
          });
        }
      }

      // كشف Evening Star
      if (this.isBullishCandle(first) && this.isSmallBody(middle) && this.isBearishCandle(last)) {
        if (middle.low > first.close && last.open < middle.low && last.close < (first.open + first.close) / 2) {
          const confidence = this.calculateThreeCandleConfidence(first, middle, last, 'bearish');
          this.addPattern('evening_star', i - 2, i, data, confidence, {
            priceRange: (first.close - last.close) / first.close
          });
        }
      }
    }
  }

  // كشف الأنماط المعقدة
  private async detectComplexPatterns(data: CandlestickData[]): Promise<void> {
    if (data.length < 20) return;

    // كشف Head and Shoulders
    await this.detectHeadAndShoulders(data);

    // كشف Double Top/Bottom
    await this.detectDoubleTopBottom(data);

    // كشف المثلثات
    await this.detectTriangles(data);

    // كشف الأعلام
    await this.detectFlags(data);
  }

  // كشف نمط الرأس والكتفين
  private async detectHeadAndShoulders(data: CandlestickData[]): Promise<void> {
    const peaks = this.findPeaks(data);
    const troughs = this.findTroughs(data);

    for (let i = 2; i < peaks.length; i++) {
      const leftShoulder = peaks[i - 2];
      const head = peaks[i - 1];
      const rightShoulder = peaks[i];

      // التحقق من شروط الرأس والكتفين
      if (head.price > leftShoulder.price && head.price > rightShoulder.price) {
        const shoulderDiff = Math.abs(leftShoulder.price - rightShoulder.price) / leftShoulder.price;

        if (shoulderDiff < 0.02) { // الكتفان متقاربان في السعر
          const confidence = Math.max(60, 90 - (shoulderDiff * 1000));
          const neckline = Math.min(leftShoulder.price, rightShoulder.price);

          this.addPattern('head_shoulders', leftShoulder.index, rightShoulder.index, data, confidence, {
            priceRange: (head.price - neckline) / neckline,
            trendContext: 'downtrend'
          });
        }
      }
    }

    // كشف الرأس والكتفين المقلوب
    for (let i = 2; i < troughs.length; i++) {
      const leftShoulder = troughs[i - 2];
      const head = troughs[i - 1];
      const rightShoulder = troughs[i];

      if (head.price < leftShoulder.price && head.price < rightShoulder.price) {
        const shoulderDiff = Math.abs(leftShoulder.price - rightShoulder.price) / leftShoulder.price;

        if (shoulderDiff < 0.02) {
          const confidence = Math.max(60, 90 - (shoulderDiff * 1000));
          const neckline = Math.max(leftShoulder.price, rightShoulder.price);

          this.addPattern('inverse_head_shoulders', leftShoulder.index, rightShoulder.index, data, confidence, {
            priceRange: (neckline - head.price) / head.price,
            trendContext: 'uptrend'
          });
        }
      }
    }
  }

  // كشف القمة/القاع المزدوج
  private async detectDoubleTopBottom(data: CandlestickData[]): Promise<void> {
    const peaks = this.findPeaks(data);
    const troughs = this.findTroughs(data);

    // Double Top
    for (let i = 1; i < peaks.length; i++) {
      const first = peaks[i - 1];
      const second = peaks[i];
      const priceDiff = Math.abs(first.price - second.price) / first.price;

      if (priceDiff < 0.015 && (second.index - first.index) > 10) {
        const confidence = Math.max(65, 85 - (priceDiff * 2000));
        this.addPattern('double_top', first.index, second.index, data, confidence, {
          priceRange: priceDiff
        });
      }
    }

    // Double Bottom
    for (let i = 1; i < troughs.length; i++) {
      const first = troughs[i - 1];
      const second = troughs[i];
      const priceDiff = Math.abs(first.price - second.price) / first.price;

      if (priceDiff < 0.015 && (second.index - first.index) > 10) {
        const confidence = Math.max(65, 85 - (priceDiff * 2000));
        this.addPattern('double_bottom', first.index, second.index, data, confidence, {
          priceRange: priceDiff
        });
      }
    }
  }

  // كشف المثلثات
  private async detectTriangles(data: CandlestickData[]): Promise<void> {
    if (data.length < 30) return;

    const peaks = this.findPeaks(data);
    const troughs = this.findTroughs(data);

    // Ascending Triangle
    for (let i = 2; i < peaks.length; i++) {
      const resistanceLevel = peaks.slice(i - 2, i + 1);
      const supportTrend = troughs.filter(t =>
        t.index > resistanceLevel[0].index && t.index < resistanceLevel[resistanceLevel.length - 1].index
      );

      if (this.isHorizontalLevel(resistanceLevel) && this.isAscendingTrend(supportTrend)) {
        const confidence = 70;
        this.addPattern('triangle_ascending', resistanceLevel[0].index, resistanceLevel[resistanceLevel.length - 1].index, data, confidence);
      }
    }

    // Descending Triangle
    for (let i = 2; i < troughs.length; i++) {
      const supportLevel = troughs.slice(i - 2, i + 1);
      const resistanceTrend = peaks.filter(p =>
        p.index > supportLevel[0].index && p.index < supportLevel[supportLevel.length - 1].index
      );

      if (this.isHorizontalLevel(supportLevel) && this.isDescendingTrend(resistanceTrend)) {
        const confidence = 70;
        this.addPattern('triangle_descending', supportLevel[0].index, supportLevel[supportLevel.length - 1].index, data, confidence);
      }
    }
  }

  // كشف الأعلام
  private async detectFlags(data: CandlestickData[]): Promise<void> {
    // تنفيذ كشف أنماط الأعلام
    for (let i = 20; i < data.length - 10; i++) {
      const recentData = data.slice(i - 20, i + 10);

      // البحث عن حركة قوية متبوعة بتوحيد
      const strongMove = this.detectStrongMove(recentData.slice(0, 15));
      const consolidation = this.detectConsolidation(recentData.slice(15));

      if (strongMove && consolidation) {
        const patternType = strongMove.direction === 'up' ? 'flag_bullish' : 'flag_bearish';
        const confidence = Math.min(strongMove.strength * consolidation.quality, 85);

        this.addPattern(patternType, i - 5, i + 5, data, confidence, {
          trendContext: strongMove.direction === 'up' ? 'uptrend' : 'downtrend'
        });
      }
    }
  }

  // دوال مساعدة
  private addPattern(
    patternType: string,
    startIndex: number,
    endIndex: number,
    data: CandlestickData[],
    confidence: number,
    patternData: any = {}
  ): void {
    const definition = this.patternDefinitions.get(patternType);
    if (!definition) return;

    const pattern: DetectedPattern = {
      id: `${patternType}_${startIndex}_${endIndex}_${Date.now()}`,
      name: definition.name,
      nameAr: definition.nameAr,
      type: definition.type,
      confidence: Math.round(confidence),
      startIndex,
      endIndex,
      startTime: data[startIndex].timestamp,
      endTime: data[endIndex].timestamp,
      description: definition.description,
      descriptionAr: definition.descriptionAr,
      significance: definition.significance,
      probability: this.calculateProbability(patternType, confidence),
      coordinates: this.calculateCoordinates(startIndex, endIndex, data),
      patternData: {
        ...patternData,
        trendContext: this.determineTrendContext(startIndex, data),
        volumeConfirmation: this.checkVolumeConfirmation(startIndex, endIndex, data)
      }
    };

    // حساب أهداف السعر ووقف الخسارة
    const targets = this.calculateTargets(pattern, data);
    pattern.priceTarget = targets.target;
    pattern.stopLoss = targets.stopLoss;

    this.patterns.push(pattern);
  }

  private isBullishCandle(candle: CandlestickData): boolean {
    return candle.close > candle.open;
  }

  private isBearishCandle(candle: CandlestickData): boolean {
    return candle.close < candle.open;
  }

  private isSmallBody(candle: CandlestickData): boolean {
    const bodySize = Math.abs(candle.close - candle.open);
    const totalRange = candle.high - candle.low;
    return totalRange > 0 && (bodySize / totalRange) < 0.3;
  }

  private calculateThreeCandleConfidence(
    first: CandlestickData,
    middle: CandlestickData,
    last: CandlestickData,
    type: 'bullish' | 'bearish'
  ): number {
    let confidence = 60;

    // زيادة الثقة بناءً على حجم الشموع
    const firstBody = Math.abs(first.close - first.open);
    const lastBody = Math.abs(last.close - last.open);
    const middleBody = Math.abs(middle.close - middle.open);

    if (firstBody > middleBody && lastBody > middleBody) confidence += 15;
    if (middle.volume < (first.volume + last.volume) / 2) confidence += 10;

    return Math.min(95, confidence);
  }

  private findPeaks(data: CandlestickData[]): Array<{ index: number; price: number }> {
    const peaks: Array<{ index: number; price: number }> = [];

    for (let i = 2; i < data.length - 2; i++) {
      const current = data[i].high;
      const prev1 = data[i - 1].high;
      const prev2 = data[i - 2].high;
      const next1 = data[i + 1].high;
      const next2 = data[i + 2].high;

      if (current > prev1 && current > prev2 && current > next1 && current > next2) {
        peaks.push({ index: i, price: current });
      }
    }

    return peaks;
  }

  private findTroughs(data: CandlestickData[]): Array<{ index: number; price: number }> {
    const troughs: Array<{ index: number; price: number }> = [];

    for (let i = 2; i < data.length - 2; i++) {
      const current = data[i].low;
      const prev1 = data[i - 1].low;
      const prev2 = data[i - 2].low;
      const next1 = data[i + 1].low;
      const next2 = data[i + 2].low;

      if (current < prev1 && current < prev2 && current < next1 && current < next2) {
        troughs.push({ index: i, price: current });
      }
    }

    return troughs;
  }

  private isHorizontalLevel(points: Array<{ index: number; price: number }>): boolean {
    if (points.length < 2) return false;

    const maxPrice = Math.max(...points.map(p => p.price));
    const minPrice = Math.min(...points.map(p => p.price));
    const priceRange = (maxPrice - minPrice) / minPrice;

    return priceRange < 0.02; // 2% tolerance
  }

  private isAscendingTrend(points: Array<{ index: number; price: number }>): boolean {
    if (points.length < 2) return false;

    for (let i = 1; i < points.length; i++) {
      if (points[i].price <= points[i - 1].price) return false;
    }

    return true;
  }

  private isDescendingTrend(points: Array<{ index: number; price: number }>): boolean {
    if (points.length < 2) return false;

    for (let i = 1; i < points.length; i++) {
      if (points[i].price >= points[i - 1].price) return false;
    }

    return true;
  }

  private detectStrongMove(data: CandlestickData[]): { direction: 'up' | 'down'; strength: number } | null {
    if (data.length < 5) return null;

    const startPrice = data[0].close;
    const endPrice = data[data.length - 1].close;
    const priceChange = (endPrice - startPrice) / startPrice;

    if (Math.abs(priceChange) > 0.03) { // 3% move
      return {
        direction: priceChange > 0 ? 'up' : 'down',
        strength: Math.min(100, Math.abs(priceChange) * 1000)
      };
    }

    return null;
  }

  private detectConsolidation(data: CandlestickData[]): { quality: number } | null {
    if (data.length < 3) return null;

    const prices = data.map(d => d.close);
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);
    const priceRange = (maxPrice - minPrice) / minPrice;

    if (priceRange < 0.02) { // 2% consolidation
      return { quality: Math.max(50, 100 - (priceRange * 2500)) };
    }

    return null;
  }

  private calculateProbability(patternType: string, confidence: number): number {
    // احتمالية النجاح بناءً على النوع والثقة
    const baseProbabilities: Record<string, number> = {
      'doji': 65,
      'hammer': 75,
      'shooting_star': 75,
      'engulfing_bullish': 80,
      'engulfing_bearish': 80,
      'morning_star': 85,
      'evening_star': 85,
      'head_shoulders': 90,
      'inverse_head_shoulders': 90,
      'double_top': 75,
      'double_bottom': 75,
      'triangle_ascending': 70,
      'triangle_descending': 70,
      'flag_bullish': 65,
      'flag_bearish': 65
    };

    const baseProbability = baseProbabilities[patternType] || 60;
    return Math.round(baseProbability + (confidence - 70) * 0.3);
  }

  private calculateCoordinates(startIndex: number, endIndex: number, data: CandlestickData[]): Array<{ x: number; y: number }> {
    const coordinates: Array<{ x: number; y: number }> = [];

    for (let i = startIndex; i <= endIndex; i++) {
      if (data[i]) {
        coordinates.push({
          x: i,
          y: data[i].close
        });
      }
    }

    return coordinates;
  }

  private determineTrendContext(index: number, data: CandlestickData[]): 'uptrend' | 'downtrend' | 'sideways' {
    const lookback = 20;
    const startIndex = Math.max(0, index - lookback);
    const endIndex = Math.min(data.length - 1, index);

    if (endIndex - startIndex < 10) return 'sideways';

    const startPrice = data[startIndex].close;
    const endPrice = data[endIndex].close;
    const priceChange = (endPrice - startPrice) / startPrice;

    if (priceChange > 0.02) return 'uptrend';
    if (priceChange < -0.02) return 'downtrend';
    return 'sideways';
  }

  private checkVolumeConfirmation(startIndex: number, endIndex: number, data: CandlestickData[]): boolean {
    const patternVolume = data.slice(startIndex, endIndex + 1)
      .reduce((sum, candle) => sum + candle.volume, 0) / (endIndex - startIndex + 1);

    const lookback = 20;
    const baselineStart = Math.max(0, startIndex - lookback);
    const baselineVolume = data.slice(baselineStart, startIndex)
      .reduce((sum, candle) => sum + candle.volume, 0) / (startIndex - baselineStart);

    return patternVolume > baselineVolume * 1.2; // 20% higher volume
  }

  private calculateTargets(pattern: DetectedPattern, data: CandlestickData[]): { target: number; stopLoss: number } {
    const currentPrice = data[pattern.endIndex].close;
    const patternHeight = this.calculatePatternHeight(pattern, data);

    let target: number;
    let stopLoss: number;

    if (pattern.type === 'bullish') {
      target = currentPrice + patternHeight;
      stopLoss = currentPrice - (patternHeight * 0.5);
    } else if (pattern.type === 'bearish') {
      target = currentPrice - patternHeight;
      stopLoss = currentPrice + (patternHeight * 0.5);
    } else {
      target = currentPrice;
      stopLoss = currentPrice - (patternHeight * 0.3);
    }

    return { target, stopLoss };
  }

  private calculatePatternHeight(pattern: DetectedPattern, data: CandlestickData[]): number {
    const patternData = data.slice(pattern.startIndex, pattern.endIndex + 1);
    const highs = patternData.map(d => d.high);
    const lows = patternData.map(d => d.low);

    return Math.max(...highs) - Math.min(...lows);
  }

  // حساب إحصائيات الأنماط
  getPatternStatistics(): PatternStatistics {
    const stats: PatternStatistics = {
      totalPatterns: this.patterns.length,
      bullishPatterns: this.patterns.filter(p => p.type === 'bullish').length,
      bearishPatterns: this.patterns.filter(p => p.type === 'bearish').length,
      neutralPatterns: this.patterns.filter(p => p.type === 'neutral').length,
      highConfidencePatterns: this.patterns.filter(p => p.confidence >= 80).length,
      patternsByType: {},
      averageConfidence: 0,
      successRate: 0,
      mostFrequentPattern: ''
    };

    // حساب الأنماط حسب النوع
    this.patterns.forEach(pattern => {
      stats.patternsByType[pattern.name] = (stats.patternsByType[pattern.name] || 0) + 1;
    });

    // حساب متوسط الثقة
    if (this.patterns.length > 0) {
      stats.averageConfidence = Math.round(
        this.patterns.reduce((sum, p) => sum + p.confidence, 0) / this.patterns.length
      );
    }

    // حساب معدل النجاح (محاكاة)
    stats.successRate = Math.round(75 + (stats.averageConfidence - 70) * 0.5);

    // العثور على أكثر الأنماط تكراراً
    const maxCount = Math.max(...Object.values(stats.patternsByType));
    stats.mostFrequentPattern = Object.keys(stats.patternsByType)
      .find(key => stats.patternsByType[key] === maxCount) || '';

    return stats;
  }

  // الحصول على الأنماط المكتشفة
  getDetectedPatterns(): DetectedPattern[] {
    return this.patterns;
  }

  // تصفية الأنماط حسب النوع
  getPatternsByType(type: 'bullish' | 'bearish' | 'neutral'): DetectedPattern[] {
    return this.patterns.filter(pattern => pattern.type === type);
  }

  // تصفية الأنماط حسب الأهمية
  getPatternsBySignificance(significance: 'low' | 'medium' | 'high' | 'critical'): DetectedPattern[] {
    return this.patterns.filter(pattern => pattern.significance === significance);
  }

  // الحصول على أفضل الأنماط
  getTopPatterns(count: number = 10): DetectedPattern[] {
    return this.patterns.slice(0, count);
  }
}

export const patternDetectionService = new PatternDetectionService();