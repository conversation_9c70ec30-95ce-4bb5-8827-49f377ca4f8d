// Technical Indicators Service - Complete Implementation
// Based on TA-Lib standards with JavaScript implementation

export interface PriceData {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: number;
}

export interface IndicatorResult {
  name: string;
  value: number;
  signal: 'buy' | 'sell' | 'neutral';
  confidence: number;
  description: string;
}

export class TechnicalIndicators {
  
  // Moving Averages
  static sma(prices: number[], period: number): number[] {
    const result: number[] = [];
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    return result;
  }

  static ema(prices: number[], period: number): number[] {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;
    result.push(ema);
    
    // Calculate EMA for remaining values
    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
      result.push(ema);
    }
    
    return result;
  }

  static wma(prices: number[], period: number): number[] {
    const result: number[] = [];
    const weights = Array.from({ length: period }, (_, i) => i + 1);
    const weightSum = weights.reduce((a, b) => a + b, 0);
    
    for (let i = period - 1; i < prices.length; i++) {
      const weightedSum = prices.slice(i - period + 1, i + 1)
        .reduce((sum, price, idx) => sum + price * weights[idx], 0);
      result.push(weightedSum / weightSum);
    }
    
    return result;
  }

  // RSI (Relative Strength Index)
  static rsi(prices: number[], period: number = 14): number[] {
    const gains: number[] = [];
    const losses: number[] = [];
    
    // Calculate price changes
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    const result: number[] = [];
    
    // Calculate initial averages
    let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period;
    let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period;
    
    // Calculate RSI values
    for (let i = period; i < gains.length; i++) {
      avgGain = ((avgGain * (period - 1)) + gains[i]) / period;
      avgLoss = ((avgLoss * (period - 1)) + losses[i]) / period;
      
      const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
      const rsi = 100 - (100 / (1 + rs));
      result.push(rsi);
    }
    
    return result;
  }

  // MACD (Moving Average Convergence Divergence)
  static macd(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const fastEma = this.ema(prices, fastPeriod);
    const slowEma = this.ema(prices, slowPeriod);
    
    // Calculate MACD line
    const macdLine: number[] = [];
    for (let i = 0; i < Math.min(fastEma.length, slowEma.length); i++) {
      macdLine.push(fastEma[i + (slowPeriod - fastPeriod)] - slowEma[i]);
    }
    
    // Calculate signal line
    const signalLine = this.ema(macdLine, signalPeriod);
    
    // Calculate histogram
    const histogram: number[] = [];
    for (let i = 0; i < signalLine.length; i++) {
      histogram.push(macdLine[i + (signalPeriod - 1)] - signalLine[i]);
    }
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram: histogram
    };
  }

  // Bollinger Bands
  static bollingerBands(prices: number[], period: number = 20, stdDev: number = 2) {
    const sma = this.sma(prices, period);
    const upperBand: number[] = [];
    const lowerBand: number[] = [];
    
    for (let i = 0; i < sma.length; i++) {
      const slice = prices.slice(i, i + period);
      const mean = sma[i];
      const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period;
      const standardDeviation = Math.sqrt(variance);
      
      upperBand.push(mean + (standardDeviation * stdDev));
      lowerBand.push(mean - (standardDeviation * stdDev));
    }
    
    return {
      upper: upperBand,
      middle: sma,
      lower: lowerBand
    };
  }

  // Stochastic Oscillator
  static stochastic(data: PriceData[], kPeriod: number = 14, dPeriod: number = 3) {
    const kValues: number[] = [];
    
    for (let i = kPeriod - 1; i < data.length; i++) {
      const slice = data.slice(i - kPeriod + 1, i + 1);
      const highestHigh = Math.max(...slice.map(d => d.high));
      const lowestLow = Math.min(...slice.map(d => d.low));
      const currentClose = data[i].close;
      
      const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
      kValues.push(k);
    }
    
    const dValues = this.sma(kValues, dPeriod);
    
    return {
      k: kValues,
      d: dValues
    };
  }

  // Average True Range (ATR)
  static atr(data: PriceData[], period: number = 14): number[] {
    const trueRanges: number[] = [];
    
    for (let i = 1; i < data.length; i++) {
      const high = data[i].high;
      const low = data[i].low;
      const prevClose = data[i - 1].close;
      
      const tr = Math.max(
        high - low,
        Math.abs(high - prevClose),
        Math.abs(low - prevClose)
      );
      
      trueRanges.push(tr);
    }
    
    return this.sma(trueRanges, period);
  }

  // Money Flow Index (MFI)
  static mfi(data: PriceData[], period: number = 14): number[] {
    const typicalPrices: number[] = [];
    const rawMoneyFlows: number[] = [];
    
    // Calculate typical prices and raw money flows
    for (let i = 0; i < data.length; i++) {
      const typicalPrice = (data[i].high + data[i].low + data[i].close) / 3;
      typicalPrices.push(typicalPrice);
      rawMoneyFlows.push(typicalPrice * data[i].volume);
    }
    
    const result: number[] = [];
    
    for (let i = period; i < data.length; i++) {
      let positiveFlow = 0;
      let negativeFlow = 0;
      
      for (let j = i - period + 1; j <= i; j++) {
        if (typicalPrices[j] > typicalPrices[j - 1]) {
          positiveFlow += rawMoneyFlows[j];
        } else if (typicalPrices[j] < typicalPrices[j - 1]) {
          negativeFlow += rawMoneyFlows[j];
        }
      }
      
      const moneyRatio = positiveFlow / negativeFlow;
      const mfi = 100 - (100 / (1 + moneyRatio));
      result.push(mfi);
    }
    
    return result;
  }

  // Williams %R
  static williamsR(data: PriceData[], period: number = 14): number[] {
    const result: number[] = [];
    
    for (let i = period - 1; i < data.length; i++) {
      const slice = data.slice(i - period + 1, i + 1);
      const highestHigh = Math.max(...slice.map(d => d.high));
      const lowestLow = Math.min(...slice.map(d => d.low));
      const currentClose = data[i].close;
      
      const williamsR = ((highestHigh - currentClose) / (highestHigh - lowestLow)) * -100;
      result.push(williamsR);
    }
    
    return result;
  }

  // On-Balance Volume (OBV)
  static obv(data: PriceData[]): number[] {
    const result: number[] = [data[0].volume];
    
    for (let i = 1; i < data.length; i++) {
      let obvValue = result[i - 1];
      
      if (data[i].close > data[i - 1].close) {
        obvValue += data[i].volume;
      } else if (data[i].close < data[i - 1].close) {
        obvValue -= data[i].volume;
      }
      
      result.push(obvValue);
    }
    
    return result;
  }

  // Price-Volume Trend (PVT)
  static pvt(data: PriceData[]): number[] {
    const result: number[] = [0];
    
    for (let i = 1; i < data.length; i++) {
      const priceChange = (data[i].close - data[i - 1].close) / data[i - 1].close;
      const pvtValue = result[i - 1] + (priceChange * data[i].volume);
      result.push(pvtValue);
    }
    
    return result;
  }

  // Parabolic SAR
  static parabolicSAR(data: PriceData[], acceleration: number = 0.02, maximum: number = 0.2) {
    const result: number[] = [];
    let isUptrend = true;
    let af = acceleration;
    let ep = data[0].high; // Extreme Point
    let sar = data[0].low;
    
    result.push(sar);
    
    for (let i = 1; i < data.length; i++) {
      const prevSAR = sar;
      
      if (isUptrend) {
        sar = prevSAR + af * (ep - prevSAR);
        
        if (data[i].high > ep) {
          ep = data[i].high;
          af = Math.min(af + acceleration, maximum);
        }
        
        if (data[i].low <= sar) {
          isUptrend = false;
          sar = ep;
          ep = data[i].low;
          af = acceleration;
        }
      } else {
        sar = prevSAR + af * (ep - prevSAR);
        
        if (data[i].low < ep) {
          ep = data[i].low;
          af = Math.min(af + acceleration, maximum);
        }
        
        if (data[i].high >= sar) {
          isUptrend = true;
          sar = ep;
          ep = data[i].high;
          af = acceleration;
        }
      }
      
      result.push(sar);
    }
    
    return result;
  }

  // Fibonacci Retracement Levels
  static fibonacciRetracement(high: number, low: number) {
    const diff = high - low;
    return {
      level_0: high,
      level_236: high - (diff * 0.236),
      level_382: high - (diff * 0.382),
      level_500: high - (diff * 0.5),
      level_618: high - (diff * 0.618),
      level_786: high - (diff * 0.786),
      level_100: low
    };
  }

  // Ichimoku Cloud
  static ichimoku(data: PriceData[], tenkanPeriod: number = 9, kijunPeriod: number = 26, senkouPeriod: number = 52) {
    const tenkanSen: number[] = [];
    const kijunSen: number[] = [];
    const senkouSpanA: number[] = [];
    const senkouSpanB: number[] = [];
    
    // Calculate Tenkan-sen (Conversion Line)
    for (let i = tenkanPeriod - 1; i < data.length; i++) {
      const slice = data.slice(i - tenkanPeriod + 1, i + 1);
      const high = Math.max(...slice.map(d => d.high));
      const low = Math.min(...slice.map(d => d.low));
      tenkanSen.push((high + low) / 2);
    }
    
    // Calculate Kijun-sen (Base Line)
    for (let i = kijunPeriod - 1; i < data.length; i++) {
      const slice = data.slice(i - kijunPeriod + 1, i + 1);
      const high = Math.max(...slice.map(d => d.high));
      const low = Math.min(...slice.map(d => d.low));
      kijunSen.push((high + low) / 2);
    }
    
    // Calculate Senkou Span A (Leading Span A)
    for (let i = 0; i < Math.min(tenkanSen.length, kijunSen.length); i++) {
      const tenkan = tenkanSen[i + Math.max(0, kijunPeriod - tenkanPeriod)];
      const kijun = kijunSen[i];
      senkouSpanA.push((tenkan + kijun) / 2);
    }
    
    // Calculate Senkou Span B (Leading Span B)
    for (let i = senkouPeriod - 1; i < data.length; i++) {
      const slice = data.slice(i - senkouPeriod + 1, i + 1);
      const high = Math.max(...slice.map(d => d.high));
      const low = Math.min(...slice.map(d => d.low));
      senkouSpanB.push((high + low) / 2);
    }
    
    return {
      tenkanSen,
      kijunSen,
      senkouSpanA,
      senkouSpanB,
      chikouSpan: data.map(d => d.close) // Lagging Span (shifted back)
    };
  }

  // VWAP (Volume Weighted Average Price)
  static vwap(data: PriceData[]): number[] {
    const result: number[] = [];
    let cumulativeVolume = 0;
    let cumulativePriceVolume = 0;
    
    for (let i = 0; i < data.length; i++) {
      const typicalPrice = (data[i].high + data[i].low + data[i].close) / 3;
      cumulativeVolume += data[i].volume;
      cumulativePriceVolume += typicalPrice * data[i].volume;
      
      result.push(cumulativePriceVolume / cumulativeVolume);
    }
    
    return result;
  }

  // Comprehensive Analysis
  static analyzeAll(data: PriceData[]): IndicatorResult[] {
    const prices = data.map(d => d.close);
    const latest = prices[prices.length - 1];
    const results: IndicatorResult[] = [];
    
    try {
      // SMA Analysis
      const sma20 = this.sma(prices, 20);
      if (sma20.length > 0) {
        const smaValue = sma20[sma20.length - 1];
        results.push({
          name: 'SMA(20)',
          value: smaValue,
          signal: latest > smaValue ? 'buy' : 'sell',
          confidence: Math.min(Math.abs(latest - smaValue) / latest * 100, 100),
          description: latest > smaValue ? 'Price above SMA - Bullish' : 'Price below SMA - Bearish'
        });
      }
      
      // RSI Analysis
      const rsi = this.rsi(prices, 14);
      if (rsi.length > 0) {
        const rsiValue = rsi[rsi.length - 1];
        let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
        let description = 'RSI in neutral zone';
        
        if (rsiValue < 30) {
          signal = 'buy';
          description = 'RSI oversold - Buy signal';
        } else if (rsiValue > 70) {
          signal = 'sell';
          description = 'RSI overbought - Sell signal';
        }
        
        results.push({
          name: 'RSI(14)',
          value: rsiValue,
          signal,
          confidence: signal === 'neutral' ? 0 : Math.abs(rsiValue - 50) * 2,
          description
        });
      }
      
      // MACD Analysis
      const macdData = this.macd(prices);
      if (macdData.macd.length > 0 && macdData.signal.length > 0) {
        const macdValue = macdData.macd[macdData.macd.length - 1];
        const signalValue = macdData.signal[macdData.signal.length - 1];
        
        results.push({
          name: 'MACD',
          value: macdValue,
          signal: macdValue > signalValue ? 'buy' : 'sell',
          confidence: Math.min(Math.abs(macdValue - signalValue) * 10, 100),
          description: macdValue > signalValue ? 'MACD above signal - Bullish' : 'MACD below signal - Bearish'
        });
      }
      
      // Bollinger Bands Analysis
      const bb = this.bollingerBands(prices, 20, 2);
      if (bb.upper.length > 0) {
        const upperBand = bb.upper[bb.upper.length - 1];
        const lowerBand = bb.lower[bb.lower.length - 1];
        const middleBand = bb.middle[bb.middle.length - 1];
        
        let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
        let description = 'Price within Bollinger Bands';
        
        if (latest <= lowerBand) {
          signal = 'buy';
          description = 'Price at lower Bollinger Band - Buy signal';
        } else if (latest >= upperBand) {
          signal = 'sell';
          description = 'Price at upper Bollinger Band - Sell signal';
        }
        
        results.push({
          name: 'Bollinger Bands',
          value: middleBand,
          signal,
          confidence: signal === 'neutral' ? 0 : 75,
          description
        });
      }
      
      // Stochastic Analysis
      if (data.length >= 14) {
        const stoch = this.stochastic(data, 14, 3);
        if (stoch.k.length > 0) {
          const kValue = stoch.k[stoch.k.length - 1];
          
          let signal: 'buy' | 'sell' | 'neutral' = 'neutral';
          let description = 'Stochastic in neutral zone';
          
          if (kValue < 20) {
            signal = 'buy';
            description = 'Stochastic oversold - Buy signal';
          } else if (kValue > 80) {
            signal = 'sell';
            description = 'Stochastic overbought - Sell signal';
          }
          
          results.push({
            name: 'Stochastic',
            value: kValue,
            signal,
            confidence: signal === 'neutral' ? 0 : Math.abs(kValue - 50) * 1.5,
            description
          });
        }
      }
      
    } catch (error) {
      console.error('Error in technical analysis:', error);
    }
    
    return results;
  }
} 