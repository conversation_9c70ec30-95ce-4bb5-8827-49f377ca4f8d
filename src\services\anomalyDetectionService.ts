import * as tf from '@tensorflow/tfjs';

// واجهات البيانات لكشف الأحداث غير المتوقعة
export interface AnomalyDetection {
  isAnomaly: boolean;
  anomalyScore: number;
  confidence: number;
  type: 'price' | 'volume' | 'pattern' | 'market' | 'news';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: number;
  affectedMetrics: string[];
  recommendations: string[];
}

export interface MarketEvent {
  id: string;
  type: 'flash_crash' | 'pump_dump' | 'unusual_volume' | 'price_gap' | 'correlation_break';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: number;
  duration: number;
  affectedAssets: string[];
  probability: number;
  impact: {
    priceChange: number;
    volumeChange: number;
    volatilityIncrease: number;
  };
}

export interface AnomalyAlert {
  id: string;
  title: string;
  message: string;
  type: 'warning' | 'danger' | 'info';
  timestamp: number;
  isRead: boolean;
  actionRequired: boolean;
  relatedAssets: string[];
}

class AnomalyDetectionService {
  private model: tf.LayersModel | null = null;
  private historicalData: number[][] = [];
  private thresholds = {
    price: 0.05,      // 5% تغيير في السعر
    volume: 2.0,      // ضعف الحجم العادي
    volatility: 0.03, // 3% تقلبات
    correlation: 0.7  // انكسار الارتباط
  };

  constructor() {
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    console.log('🚨 Initializing Anomaly Detection Service...');
    
    // إعداد النموذج الأساسي لكشف الشذوذ
    await this.initializeModel();
  }

  // إعداد نموذج كشف الشذوذ
  private async initializeModel(): Promise<void> {
    try {
      // إنشاء نموذج Autoencoder بسيط لكشف الشذوذ
      this.model = tf.sequential({
        layers: [
          tf.layers.dense({ inputShape: [10], units: 8, activation: 'relu' }),
          tf.layers.dense({ units: 4, activation: 'relu' }),
          tf.layers.dense({ units: 2, activation: 'relu' }),
          tf.layers.dense({ units: 4, activation: 'relu' }),
          tf.layers.dense({ units: 8, activation: 'relu' }),
          tf.layers.dense({ units: 10, activation: 'linear' })
        ]
      });

      this.model.compile({
        optimizer: 'adam',
        loss: 'meanSquaredError'
      });

      console.log('✅ Anomaly detection model initialized');
    } catch (error) {
      console.error('❌ Error initializing anomaly detection model:', error);
    }
  }

  // كشف الأحداث غير المتوقعة في البيانات
  async detectAnomalies(
    marketData: {
      price: number;
      volume: number;
      timestamp: number;
      symbol: string;
    }[]
  ): Promise<AnomalyDetection[]> {
    console.log('🔍 Detecting anomalies in market data...');

    const anomalies: AnomalyDetection[] = [];

    for (let i = 1; i < marketData.length; i++) {
      const current = marketData[i];
      const previous = marketData[i - 1];

      // كشف شذوذ السعر
      const priceAnomaly = await this.detectPriceAnomaly(current, previous);
      if (priceAnomaly) anomalies.push(priceAnomaly);

      // كشف شذوذ الحجم
      const volumeAnomaly = await this.detectVolumeAnomaly(current, marketData.slice(Math.max(0, i - 20), i));
      if (volumeAnomaly) anomalies.push(volumeAnomaly);

      // كشف أنماط غير عادية
      const patternAnomaly = await this.detectPatternAnomaly(marketData.slice(Math.max(0, i - 10), i + 1));
      if (patternAnomaly) anomalies.push(patternAnomaly);
    }

    return anomalies;
  }

  // كشف شذوذ السعر
  private async detectPriceAnomaly(
    current: { price: number; timestamp: number; symbol: string },
    previous: { price: number; timestamp: number; symbol: string }
  ): Promise<AnomalyDetection | null> {
    const priceChange = Math.abs(current.price - previous.price) / previous.price;

    if (priceChange > this.thresholds.price) {
      const severity = this.calculateSeverity(priceChange, this.thresholds.price);
      
      return {
        isAnomaly: true,
        anomalyScore: priceChange,
        confidence: Math.min(priceChange / this.thresholds.price, 1.0),
        type: 'price',
        severity,
        description: `تغيير سعر غير عادي بنسبة ${(priceChange * 100).toFixed(2)}% في ${current.symbol}`,
        timestamp: current.timestamp,
        affectedMetrics: ['price', 'volatility'],
        recommendations: [
          'مراقبة السعر عن كثب',
          'التحقق من الأخبار المتعلقة بالأصل',
          'تقييم إعادة توازن المحفظة'
        ]
      };
    }

    return null;
  }

  // كشف شذوذ الحجم
  private async detectVolumeAnomaly(
    current: { volume: number; timestamp: number; symbol: string },
    historicalData: { volume: number }[]
  ): Promise<AnomalyDetection | null> {
    if (historicalData.length === 0) return null;

    const avgVolume = historicalData.reduce((sum, data) => sum + data.volume, 0) / historicalData.length;
    const volumeRatio = current.volume / avgVolume;

    if (volumeRatio > this.thresholds.volume) {
      const severity = this.calculateSeverity(volumeRatio, this.thresholds.volume);
      
      return {
        isAnomaly: true,
        anomalyScore: volumeRatio,
        confidence: Math.min(volumeRatio / this.thresholds.volume / 2, 1.0),
        type: 'volume',
        severity,
        description: `حجم تداول غير عادي بنسبة ${(volumeRatio * 100).toFixed(0)}% من المتوسط في ${current.symbol}`,
        timestamp: current.timestamp,
        affectedMetrics: ['volume', 'liquidity'],
        recommendations: [
          'التحقق من أسباب زيادة الحجم',
          'مراقبة السيولة',
          'تحليل تحركات المؤسسات'
        ]
      };
    }

    return null;
  }

  // كشف أنماط غير عادية
  private async detectPatternAnomaly(
    dataWindow: { price: number; volume: number; timestamp: number; symbol: string }[]
  ): Promise<AnomalyDetection | null> {
    if (dataWindow.length < 5) return null;

    // تحليل نمط الأسعار
    const prices = dataWindow.map(d => d.price);
    const pricePattern = this.analyzePattern(prices);

    if (pricePattern.isUnusual) {
      return {
        isAnomaly: true,
        anomalyScore: pricePattern.score,
        confidence: pricePattern.confidence,
        type: 'pattern',
        severity: this.calculateSeverity(pricePattern.score, 0.5),
        description: `نمط سعري غير عادي تم اكتشافه: ${pricePattern.description}`,
        timestamp: dataWindow[dataWindow.length - 1].timestamp,
        affectedMetrics: ['price_pattern', 'trend'],
        recommendations: [
          'تحليل النمط بعمق أكبر',
          'مراجعة الاستراتيجية',
          'تقييم نقاط الدخول والخروج'
        ]
      };
    }

    return null;
  }

  // تحليل الأنماط
  private analyzePattern(prices: number[]): {
    isUnusual: boolean;
    score: number;
    confidence: number;
    description: string;
  } {
    // حساب التغييرات المتتالية
    const changes = [];
    for (let i = 1; i < prices.length; i++) {
      changes.push((prices[i] - prices[i - 1]) / prices[i - 1]);
    }

    // كشف أنماط غير عادية
    const volatility = this.calculateVolatility(changes);
    const trend = this.calculateTrend(changes);
    
    // نمط الزجزاج (Zigzag)
    const zigzagScore = this.detectZigzagPattern(changes);
    
    // نمط الفجوة (Gap)
    const gapScore = this.detectGapPattern(changes);

    const maxScore = Math.max(zigzagScore, gapScore);
    const isUnusual = maxScore > 0.6 || volatility > 0.05;

    let description = 'نمط عادي';
    if (zigzagScore > 0.6) description = 'نمط زجزاج حاد';
    else if (gapScore > 0.6) description = 'فجوة سعرية كبيرة';
    else if (volatility > 0.05) description = 'تقلبات عالية غير عادية';

    return {
      isUnusual,
      score: maxScore,
      confidence: Math.min(maxScore, 1.0),
      description
    };
  }

  // كشف نمط الزجزاج
  private detectZigzagPattern(changes: number[]): number {
    let zigzagCount = 0;
    for (let i = 1; i < changes.length; i++) {
      if ((changes[i] > 0 && changes[i - 1] < 0) || (changes[i] < 0 && changes[i - 1] > 0)) {
        zigzagCount++;
      }
    }
    return zigzagCount / (changes.length - 1);
  }

  // كشف نمط الفجوة
  private detectGapPattern(changes: number[]): number {
    const maxChange = Math.max(...changes.map(Math.abs));
    return Math.min(maxChange / 0.1, 1.0); // تطبيع إلى 1
  }

  // حساب التقلبات
  private calculateVolatility(changes: number[]): number {
    const mean = changes.reduce((sum, change) => sum + change, 0) / changes.length;
    const variance = changes.reduce((sum, change) => sum + Math.pow(change - mean, 2), 0) / changes.length;
    return Math.sqrt(variance);
  }

  // حساب الاتجاه
  private calculateTrend(changes: number[]): number {
    const positiveChanges = changes.filter(change => change > 0).length;
    return positiveChanges / changes.length;
  }

  // حساب شدة الشذوذ
  private calculateSeverity(score: number, threshold: number): 'low' | 'medium' | 'high' | 'critical' {
    const ratio = score / threshold;
    if (ratio < 1.5) return 'low';
    if (ratio < 3) return 'medium';
    if (ratio < 5) return 'high';
    return 'critical';
  }

  // توليد تنبيهات الأحداث السوقية
  async generateMarketEventAlerts(anomalies: AnomalyDetection[]): Promise<AnomalyAlert[]> {
    console.log('🚨 Generating market event alerts...');

    const alerts: AnomalyAlert[] = [];

    anomalies.forEach((anomaly, index) => {
      if (anomaly.severity === 'high' || anomaly.severity === 'critical') {
        alerts.push({
          id: `alert_${Date.now()}_${index}`,
          title: `تنبيه ${anomaly.type === 'price' ? 'سعري' : anomaly.type === 'volume' ? 'حجم' : 'نمط'}`,
          message: anomaly.description,
          type: anomaly.severity === 'critical' ? 'danger' : 'warning',
          timestamp: anomaly.timestamp,
          isRead: false,
          actionRequired: anomaly.severity === 'critical',
          relatedAssets: [] // سيتم ملؤها حسب البيانات
        });
      }
    });

    return alerts;
  }

  // تحديث العتبات
  updateThresholds(newThresholds: Partial<typeof this.thresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    console.log('📊 Anomaly detection thresholds updated:', this.thresholds);
  }

  // الحصول على إحصائيات الشذوذ
  getAnomalyStatistics(anomalies: AnomalyDetection[]): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    averageScore: number;
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};
    let totalScore = 0;

    anomalies.forEach(anomaly => {
      byType[anomaly.type] = (byType[anomaly.type] || 0) + 1;
      bySeverity[anomaly.severity] = (bySeverity[anomaly.severity] || 0) + 1;
      totalScore += anomaly.anomalyScore;
    });

    return {
      total: anomalies.length,
      byType,
      bySeverity,
      averageScore: anomalies.length > 0 ? totalScore / anomalies.length : 0
    };
  }
}

export const anomalyDetectionService = new AnomalyDetectionService();
