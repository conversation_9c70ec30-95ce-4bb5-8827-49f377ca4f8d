
import React, { useState, useEffect } from 'react';
import { Eye, FileText, BarChart3, MessageSquare, Brain, AlertTriangle, CheckCircle, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { explainableAIService, SHAPExplanation } from '@/services/explainableAIService';

const ExplainableAI = () => {
  const [shapExplanation, setShapExplanation] = useState<SHAPExplanation | null>(null);
  const [biasAnalysis, setBiasAnalysis] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadExplanations();
  }, []);

  const loadExplanations = async () => {
    setIsLoading(true);
    try {
      // محاكاة بيانات الدخل
      const mockInputData = [0.65, 0.8, 0.45, 0.7, 0.55, 0.6, 0.75, 0.4, 0.85, 0.9];
      const mockPrediction = 0.78;

      // توليد تفسير SHAP
      const shap = await explainableAIService.generateSHAPExplanation(mockInputData, mockPrediction);
      setShapExplanation(shap);

      // تحليل التحيز
      const mockMetadata = Array.from({ length: 10 }, (_, i) => ({
        timestamp: Date.now() - i * 3600000,
        marketCondition: ['bullish', 'bearish', 'sideways'][i % 3]
      }));

      const bias = await explainableAIService.detectAdvancedBias(
        [0.8, 0.7, 0.9, 0.6, 0.85],
        [0.75, 0.72, 0.88, 0.65, 0.82],
        mockMetadata
      );
      setBiasAnalysis(bias);

    } catch (error) {
      console.error('Error loading explanations:', error);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">
          الذكاء الاصطناعي القابل للتفسير
        </h1>
        
        {/* إحصائيات محسنة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <Eye className="h-12 w-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">دقة التفسير</h3>
              <p className="text-2xl font-bold text-blue-400">
                {shapExplanation ? `${(shapExplanation.confidence * 100).toFixed(1)}%` : '98.5%'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <FileText className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">الميزات المحللة</h3>
              <p className="text-2xl font-bold text-green-400">
                {shapExplanation ? shapExplanation.featureImportance.length : '10'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-12 w-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">مستوى التحيز</h3>
              <p className="text-2xl font-bold text-purple-400">
                {biasAnalysis ? `${(biasAnalysis.overallBias * 100).toFixed(1)}%` : '12.3%'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6 text-center">
              <MessageSquare className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">التوصيات</h3>
              <p className="text-2xl font-bold text-yellow-400">
                {biasAnalysis ? biasAnalysis.recommendations.length : '3'}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-center gap-4 mb-6">
          <Button onClick={loadExplanations} disabled={isLoading} className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            {isLoading ? 'جاري التحليل...' : 'تحليل جديد'}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* تحليل SHAP */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-400" />
                تحليل SHAP - أهمية الميزات
              </CardTitle>
            </CardHeader>
            <CardContent>
              {shapExplanation ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-sm text-gray-400">التنبؤ:</span>
                    <Badge variant={shapExplanation.prediction > 0.5 ? "default" : "destructive"}>
                      {(shapExplanation.prediction * 100).toFixed(1)}%
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {shapExplanation.featureImportance.slice(0, 5).map((feature, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{feature.feature}</span>
                          <div className="flex items-center gap-2">
                            <Badge variant={feature.impact === 'positive' ? "default" : "destructive"} className="text-xs">
                              {feature.impact === 'positive' ? '+' : '-'}{(feature.importance * 100).toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                        <Progress
                          value={feature.importance * 100}
                          className="h-2"
                        />
                        <p className="text-xs text-gray-400">{feature.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">اضغط على "تحليل جديد" لعرض تحليل SHAP</p>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* تحليل التحيز */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
                تحليل التحيز والعدالة
              </CardTitle>
            </CardHeader>
            <CardContent>
              {biasAnalysis ? (
                <div className="space-y-6">
                  {/* مؤشرات التحيز */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-slate-700 rounded-lg">
                      <p className="text-sm text-gray-400 mb-1">التحيز الزمني</p>
                      <p className="text-xl font-bold text-blue-400">
                        {(biasAnalysis.temporalBias * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div className="text-center p-4 bg-slate-700 rounded-lg">
                      <p className="text-sm text-gray-400 mb-1">تحيز السوق</p>
                      <p className="text-xl font-bold text-purple-400">
                        {(biasAnalysis.marketBias * 100).toFixed(1)}%
                      </p>
                    </div>
                  </div>

                  {/* مؤشر التحيز الإجمالي */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">التحيز الإجمالي</span>
                      <Badge variant={biasAnalysis.overallBias < 0.3 ? "default" : biasAnalysis.overallBias < 0.6 ? "secondary" : "destructive"}>
                        {biasAnalysis.overallBias < 0.3 ? 'منخفض' : biasAnalysis.overallBias < 0.6 ? 'متوسط' : 'عالي'}
                      </Badge>
                    </div>
                    <Progress
                      value={biasAnalysis.overallBias * 100}
                      className="h-3"
                    />
                  </div>

                  {/* التوصيات */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-white flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      التوصيات
                    </h4>
                    <div className="space-y-2">
                      {biasAnalysis.recommendations.map((rec: string, index: number) => (
                        <div key={index} className="flex items-start gap-2 p-2 bg-slate-700 rounded">
                          <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                          <p className="text-sm text-gray-300">{rec}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">اضغط على "تحليل جديد" لعرض تحليل التحيز</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ExplainableAI;
