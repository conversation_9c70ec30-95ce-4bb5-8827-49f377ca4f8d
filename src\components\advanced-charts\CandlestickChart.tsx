import React, { useEffect, useRef, useState } from 'react';
import { Chart as ChartJS, CategoryScale, TimeScale, LinearScale } from 'chart.js';
import { Chart } from 'react-chartjs-2';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Play, Pause, Download } from 'lucide-react';

ChartJS.register(CategoryScale, TimeScale, LinearScale);

interface CandlestickChartProps {
  symbol: string;
  timeframe: string;
  lang: 'en' | 'ar';
}

export function CandlestickChart({ symbol, timeframe, lang }: CandlestickChartProps) {
  const [isRealTime, setIsRealTime] = useState(false);
  const [lastPrice, setLastPrice] = useState(45230.50);
  const [priceChange, setPriceChange] = useState(1105.30);
  const [priceChangePercent, setPriceChangePercent] = useState(2.45);

  // Generate realistic candlestick data
  const generateCandleData = () => {
    const data = [];
    let basePrice = 45000;
    for (let i = 0; i < 50; i++) {
      const time = Date.now() - (50 - i) * 3600000;
      const open = basePrice;
      const change = (Math.random() - 0.5) * 0.03;
      const close = open * (1 + change);
      const high = Math.max(open, close) * (1 + Math.random() * 0.01);
      const low = Math.min(open, close) * (1 - Math.random() * 0.01);
      
      data.push({
        x: time,
        o: open,
        h: high,
        l: low,
        c: close,
        isGreen: close >= open
      });
      
      basePrice = close;
    }
    return data;
  };

  const candleData = generateCandleData();

  const chartData = {
    datasets: [
      {
        label: symbol,
        data: candleData,
        backgroundColor: (ctx: any) => {
          return ctx.raw?.isGreen ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)';
        },
        borderColor: (ctx: any) => {
          return ctx.raw?.isGreen ? '#10B981' : '#EF4444';
        },
        borderWidth: 1,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        type: 'time' as const,
        grid: { color: '#374151' },
        ticks: { color: '#9CA3AF' }
      },
      y: {
        grid: { color: '#374151' },
        ticks: { 
          color: '#9CA3AF',
          callback: (value: any) => '$' + value.toLocaleString()
        }
      }
    },
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: '#1F2937',
        titleColor: '#F9FAFB',
        bodyColor: '#D1D5DB'
      }
    }
  };

  return (
    <Card className="w-full bg-slate-800 border-slate-700">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-400" />
            {symbol} - {timeframe}
          </CardTitle>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-white">
                ${lastPrice.toLocaleString()}
              </span>
              <Badge variant={priceChange >= 0 ? "default" : "destructive"}>
                {priceChange >= 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                +{priceChangePercent}%
              </Badge>
            </div>
            <div className="flex gap-2">
              <Button
                variant={isRealTime ? "default" : "outline"}
                size="sm"
                onClick={() => setIsRealTime(!isRealTime)}
              >
                {isRealTime ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                {lang === 'ar' ? (isRealTime ? 'إيقاف' : 'مباشر') : (isRealTime ? 'Stop' : 'Live')}
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-96">
          <Chart type="bar" data={chartData} options={options} />
        </div>
        
        {/* Technical Indicators */}
        <div className="mt-4 flex flex-wrap gap-2">
          <Badge variant="default" className="text-xs">SMA(20): $44,567</Badge>
          <Badge variant="secondary" className="text-xs">RSI(14): 68.4</Badge>
          <Badge variant="outline" className="text-xs">MACD: +234.5</Badge>
        </div>

        {/* Pattern Detection */}
        <div className="mt-4">
          <h4 className="text-sm font-semibold text-white mb-2">
            {lang === 'ar' ? 'الأنماط المكتشفة' : 'Detected Patterns'}
          </h4>
          <div className="space-y-2">
            <div className="p-3 rounded-lg bg-green-900/20 border border-green-700">
              <div className="flex justify-between items-center">
                <span className="font-medium text-white">
                  {lang === 'ar' ? 'نمط المطرقة' : 'Hammer Pattern'}
                </span>
                <Badge variant="outline" className="text-xs">85% {lang === 'ar' ? 'ثقة' : 'confidence'}</Badge>
              </div>
              <p className="text-sm text-gray-300 mt-1">
                {lang === 'ar' 
                  ? 'نمط صعودي يشير إلى انعكاس محتمل من الهبوط'
                  : 'Bullish pattern indicating potential reversal from downtrend'
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 