// خدمة تصدير التقارير المتقدمة CSV/JSON
import { CandlestickData, TechnicalIndicator, MarketData } from './enhancedMarketDataService';
import { DetectedPattern, PatternStatistics } from './patternDetectionService';

export interface ExportOptions {
  format: 'csv' | 'json' | 'excel' | 'pdf';
  includePatterns: boolean;
  includeIndicators: boolean;
  includeStatistics: boolean;
  includeChartData: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  customFields?: string[];
  compression?: boolean;
  language: 'ar' | 'en';
}

export interface ExportData {
  metadata: {
    symbol: string;
    timeframe: string;
    exportDate: string;
    dataPoints: number;
    exportOptions: ExportOptions;
    version: string;
  };
  marketData?: {
    candlesticks: CandlestickData[];
    indicators: TechnicalIndicator[];
    supportLevels: number[];
    resistanceLevels: number[];
  };
  patterns?: DetectedPattern[];
  statistics?: {
    market: any;
    patterns: PatternStatistics;
    performance: any;
  };
  analysis?: {
    summary: string;
    recommendations: string[];
    riskAssessment: string;
  };
}

class ExportService {
  private readonly version = '2.0.0';

  constructor() {
    console.log('📊 Export Service initialized');
  }

  // تصدير البيانات الكاملة
  async exportCompleteData(
    marketData: MarketData,
    patterns: DetectedPattern[],
    options: ExportOptions
  ): Promise<string> {
    console.log(`📤 Exporting data in ${options.format} format`);

    const exportData = this.prepareExportData(marketData, patterns, options);

    switch (options.format) {
      case 'csv':
        return this.exportToCSV(exportData, options);
      case 'json':
        return this.exportToJSON(exportData, options);
      case 'excel':
        return this.exportToExcel(exportData, options);
      case 'pdf':
        return this.exportToPDF(exportData, options);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  // تحضير بيانات التصدير
  private prepareExportData(
    marketData: MarketData,
    patterns: DetectedPattern[],
    options: ExportOptions
  ): ExportData {
    const exportData: ExportData = {
      metadata: {
        symbol: marketData.symbol,
        timeframe: marketData.timeframe,
        exportDate: new Date().toISOString(),
        dataPoints: marketData.candlesticks.length,
        exportOptions: options,
        version: this.version
      }
    };

    // إضافة بيانات السوق
    if (options.includeChartData) {
      exportData.marketData = {
        candlesticks: this.filterDataByDateRange(marketData.candlesticks, options.dateRange),
        indicators: options.includeIndicators ? marketData.indicators : [],
        supportLevels: marketData.supportLevels,
        resistanceLevels: marketData.resistanceLevels
      };
    }

    // إضافة الأنماط
    if (options.includePatterns) {
      exportData.patterns = this.filterPatternsByDateRange(patterns, options.dateRange);
    }

    // إضافة الإحصائيات
    if (options.includeStatistics) {
      exportData.statistics = this.generateStatistics(marketData, patterns);
    }

    // إضافة التحليل
    exportData.analysis = this.generateAnalysis(marketData, patterns, options.language);

    return exportData;
  }

  // تصدير إلى CSV
  private exportToCSV(data: ExportData, options: ExportOptions): string {
    let csv = '';
    const lang = options.language;

    // إضافة البيانات الأساسية
    csv += this.generateCSVHeader(lang) + '\n';
    csv += this.generateMetadataCSV(data.metadata, lang) + '\n\n';

    // إضافة بيانات الشموع
    if (data.marketData?.candlesticks) {
      csv += this.generateCandlestickCSV(data.marketData.candlesticks, lang) + '\n\n';
    }

    // إضافة المؤشرات الفنية
    if (data.marketData?.indicators && options.includeIndicators) {
      csv += this.generateIndicatorsCSV(data.marketData.indicators, lang) + '\n\n';
    }

    // إضافة الأنماط
    if (data.patterns && options.includePatterns) {
      csv += this.generatePatternsCSV(data.patterns, lang) + '\n\n';
    }

    // إضافة الإحصائيات
    if (data.statistics && options.includeStatistics) {
      csv += this.generateStatisticsCSV(data.statistics, lang) + '\n\n';
    }

    // إضافة التحليل
    if (data.analysis) {
      csv += this.generateAnalysisCSV(data.analysis, lang);
    }

    return csv;
  }

  // تصدير إلى JSON
  private exportToJSON(data: ExportData, options: ExportOptions): string {
    const jsonData = {
      ...data,
      exportInfo: {
        format: 'json',
        timestamp: new Date().toISOString(),
        language: options.language,
        compressed: options.compression || false
      }
    };

    return JSON.stringify(jsonData, null, options.compression ? 0 : 2);
  }

  // تصدير إلى Excel (محاكاة)
  private exportToExcel(data: ExportData, options: ExportOptions): string {
    // في التطبيق الحقيقي، سنستخدم مكتبة مثل xlsx
    const excelData = {
      worksheets: {
        'Market Data': data.marketData?.candlesticks || [],
        'Patterns': data.patterns || [],
        'Statistics': data.statistics || {},
        'Analysis': data.analysis || {}
      },
      metadata: data.metadata
    };

    return JSON.stringify(excelData, null, 2);
  }

  // تصدير إلى PDF (محاكاة)
  private exportToPDF(data: ExportData, options: ExportOptions): string {
    const lang = options.language;
    
    const pdfContent = {
      title: lang === 'ar' ? 'تقرير تحليل السوق' : 'Market Analysis Report',
      subtitle: `${data.metadata.symbol} - ${data.metadata.timeframe}`,
      sections: [
        {
          title: lang === 'ar' ? 'ملخص تنفيذي' : 'Executive Summary',
          content: data.analysis?.summary || ''
        },
        {
          title: lang === 'ar' ? 'الأنماط المكتشفة' : 'Detected Patterns',
          content: data.patterns?.length || 0
        },
        {
          title: lang === 'ar' ? 'التوصيات' : 'Recommendations',
          content: data.analysis?.recommendations || []
        },
        {
          title: lang === 'ar' ? 'تقييم المخاطر' : 'Risk Assessment',
          content: data.analysis?.riskAssessment || ''
        }
      ],
      footer: `Generated on ${new Date().toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US')}`
    };

    return JSON.stringify(pdfContent, null, 2);
  }

  // توليد عنوان CSV
  private generateCSVHeader(lang: 'ar' | 'en'): string {
    const title = lang === 'ar' ? 'تقرير تحليل السوق المتقدم' : 'Advanced Market Analysis Report';
    return `# ${title}\n# Generated on: ${new Date().toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US')}`;
  }

  // توليد بيانات التعريف CSV
  private generateMetadataCSV(metadata: ExportData['metadata'], lang: 'ar' | 'en'): string {
    const headers = lang === 'ar' 
      ? ['المعرف', 'القيمة']
      : ['Field', 'Value'];
    
    const rows = [
      [lang === 'ar' ? 'الرمز' : 'Symbol', metadata.symbol],
      [lang === 'ar' ? 'الإطار الزمني' : 'Timeframe', metadata.timeframe],
      [lang === 'ar' ? 'نقاط البيانات' : 'Data Points', metadata.dataPoints.toString()],
      [lang === 'ar' ? 'تاريخ التصدير' : 'Export Date', metadata.exportDate],
      [lang === 'ar' ? 'الإصدار' : 'Version', metadata.version]
    ];

    return this.arrayToCSV([headers, ...rows]);
  }

  // توليد بيانات الشموع CSV
  private generateCandlestickCSV(candlesticks: CandlestickData[], lang: 'ar' | 'en'): string {
    const headers = lang === 'ar'
      ? ['التاريخ', 'الوقت', 'فتح', 'أعلى', 'أقل', 'إغلاق', 'الحجم']
      : ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume'];

    const rows = candlesticks.map(candle => {
      const date = new Date(candle.timestamp);
      return [
        date.toLocaleDateString(lang === 'ar' ? 'ar-SA' : 'en-US'),
        date.toLocaleTimeString(lang === 'ar' ? 'ar-SA' : 'en-US'),
        candle.open.toFixed(5),
        candle.high.toFixed(5),
        candle.low.toFixed(5),
        candle.close.toFixed(5),
        candle.volume.toString()
      ];
    });

    return `\n# ${lang === 'ar' ? 'بيانات الشموع اليابانية' : 'Candlestick Data'}\n` + 
           this.arrayToCSV([headers, ...rows]);
  }

  // توليد بيانات المؤشرات CSV
  private generateIndicatorsCSV(indicators: TechnicalIndicator[], lang: 'ar' | 'en'): string {
    if (indicators.length === 0) return '';

    let csv = `\n# ${lang === 'ar' ? 'المؤشرات الفنية' : 'Technical Indicators'}\n`;

    indicators.forEach(indicator => {
      const headers = [
        lang === 'ar' ? 'الفهرس' : 'Index',
        lang === 'ar' ? indicator.name : indicator.name,
        lang === 'ar' ? 'النوع' : 'Type',
        lang === 'ar' ? 'اللون' : 'Color'
      ];

      const rows = indicator.values.map((value, index) => [
        index.toString(),
        value.toFixed(4),
        indicator.type,
        indicator.color
      ]);

      csv += this.arrayToCSV([headers, ...rows.slice(0, 10)]) + '\n\n'; // أول 10 قيم فقط
    });

    return csv;
  }

  // توليد بيانات الأنماط CSV
  private generatePatternsCSV(patterns: DetectedPattern[], lang: 'ar' | 'en'): string {
    if (patterns.length === 0) return '';

    const headers = lang === 'ar'
      ? ['الاسم', 'النوع', 'الثقة', 'البداية', 'النهاية', 'الأهمية', 'الوصف']
      : ['Name', 'Type', 'Confidence', 'Start', 'End', 'Significance', 'Description'];

    const rows = patterns.map(pattern => [
      lang === 'ar' ? pattern.nameAr : pattern.name,
      pattern.type,
      `${pattern.confidence}%`,
      new Date(pattern.startTime).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US'),
      new Date(pattern.endTime).toLocaleString(lang === 'ar' ? 'ar-SA' : 'en-US'),
      pattern.significance,
      lang === 'ar' ? pattern.descriptionAr : pattern.description
    ]);

    return `\n# ${lang === 'ar' ? 'الأنماط المكتشفة' : 'Detected Patterns'}\n` + 
           this.arrayToCSV([headers, ...rows]);
  }

  // توليد بيانات الإحصائيات CSV
  private generateStatisticsCSV(statistics: any, lang: 'ar' | 'en'): string {
    const headers = lang === 'ar' 
      ? ['الإحصائية', 'القيمة']
      : ['Statistic', 'Value'];

    const rows = [
      [lang === 'ar' ? 'إجمالي الأنماط' : 'Total Patterns', statistics.patterns?.totalPatterns?.toString() || '0'],
      [lang === 'ar' ? 'الأنماط الصعودية' : 'Bullish Patterns', statistics.patterns?.bullishPatterns?.toString() || '0'],
      [lang === 'ar' ? 'الأنماط الهبوطية' : 'Bearish Patterns', statistics.patterns?.bearishPatterns?.toString() || '0'],
      [lang === 'ar' ? 'متوسط الثقة' : 'Average Confidence', `${statistics.patterns?.averageConfidence || 0}%`],
      [lang === 'ar' ? 'معدل النجاح' : 'Success Rate', `${statistics.patterns?.successRate || 0}%`]
    ];

    return `\n# ${lang === 'ar' ? 'الإحصائيات' : 'Statistics'}\n` + 
           this.arrayToCSV([headers, ...rows]);
  }

  // توليد بيانات التحليل CSV
  private generateAnalysisCSV(analysis: ExportData['analysis'], lang: 'ar' | 'en'): string {
    if (!analysis) return '';

    let csv = `\n# ${lang === 'ar' ? 'التحليل والتوصيات' : 'Analysis & Recommendations'}\n`;
    
    csv += `\n## ${lang === 'ar' ? 'الملخص' : 'Summary'}\n`;
    csv += `"${analysis.summary}"\n\n`;
    
    csv += `## ${lang === 'ar' ? 'التوصيات' : 'Recommendations'}\n`;
    analysis.recommendations?.forEach((rec, index) => {
      csv += `${index + 1}. "${rec}"\n`;
    });
    
    csv += `\n## ${lang === 'ar' ? 'تقييم المخاطر' : 'Risk Assessment'}\n`;
    csv += `"${analysis.riskAssessment}"\n`;

    return csv;
  }

  // تحويل مصفوفة إلى CSV
  private arrayToCSV(data: string[][]): string {
    return data.map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
  }

  // تصفية البيانات حسب النطاق الزمني
  private filterDataByDateRange(data: CandlestickData[], dateRange?: { start: Date; end: Date }): CandlestickData[] {
    if (!dateRange) return data;

    return data.filter(candle => {
      const candleDate = new Date(candle.timestamp);
      return candleDate >= dateRange.start && candleDate <= dateRange.end;
    });
  }

  // تصفية الأنماط حسب النطاق الزمني
  private filterPatternsByDateRange(patterns: DetectedPattern[], dateRange?: { start: Date; end: Date }): DetectedPattern[] {
    if (!dateRange) return patterns;

    return patterns.filter(pattern => {
      const patternDate = new Date(pattern.startTime);
      return patternDate >= dateRange.start && patternDate <= dateRange.end;
    });
  }

  // توليد الإحصائيات
  private generateStatistics(marketData: MarketData, patterns: DetectedPattern[]): any {
    return {
      market: {
        totalCandles: marketData.candlesticks.length,
        priceRange: this.calculatePriceRange(marketData.candlesticks),
        averageVolume: this.calculateAverageVolume(marketData.candlesticks),
        volatility: this.calculateVolatility(marketData.candlesticks)
      },
      patterns: {
        totalPatterns: patterns.length,
        bullishPatterns: patterns.filter(p => p.type === 'bullish').length,
        bearishPatterns: patterns.filter(p => p.type === 'bearish').length,
        averageConfidence: patterns.length > 0 
          ? Math.round(patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length)
          : 0,
        successRate: 75 // محاكاة
      }
    };
  }

  // توليد التحليل
  private generateAnalysis(marketData: MarketData, patterns: DetectedPattern[], lang: 'ar' | 'en'): ExportData['analysis'] {
    const bullishPatterns = patterns.filter(p => p.type === 'bullish').length;
    const bearishPatterns = patterns.filter(p => p.type === 'bearish').length;
    const highConfidencePatterns = patterns.filter(p => p.confidence >= 80).length;

    const summary = lang === 'ar' 
      ? `تم تحليل ${marketData.candlesticks.length} شمعة وكشف ${patterns.length} نمط. ${bullishPatterns} أنماط صعودية و ${bearishPatterns} أنماط هبوطية. ${highConfidencePatterns} أنماط عالية الثقة.`
      : `Analyzed ${marketData.candlesticks.length} candles and detected ${patterns.length} patterns. ${bullishPatterns} bullish and ${bearishPatterns} bearish patterns. ${highConfidencePatterns} high-confidence patterns.`;

    const recommendations = lang === 'ar' ? [
      bullishPatterns > bearishPatterns ? 'الاتجاه العام صعودي - فكر في الشراء' : 'الاتجاه العام هبوطي - فكر في البيع',
      highConfidencePatterns > 3 ? 'توجد إشارات قوية في السوق' : 'إشارات السوق ضعيفة - توخ الحذر',
      'استخدم وقف الخسارة دائماً',
      'راقب مستويات الدعم والمقاومة'
    ] : [
      bullishPatterns > bearishPatterns ? 'Overall trend is bullish - consider buying' : 'Overall trend is bearish - consider selling',
      highConfidencePatterns > 3 ? 'Strong market signals present' : 'Weak market signals - exercise caution',
      'Always use stop-loss orders',
      'Monitor support and resistance levels'
    ];

    const riskAssessment = lang === 'ar'
      ? `مستوى المخاطر: ${highConfidencePatterns > 5 ? 'منخفض' : highConfidencePatterns > 2 ? 'متوسط' : 'عالي'}. يُنصح بإدارة المخاطر بعناية.`
      : `Risk Level: ${highConfidencePatterns > 5 ? 'Low' : highConfidencePatterns > 2 ? 'Medium' : 'High'}. Careful risk management advised.`;

    return {
      summary,
      recommendations,
      riskAssessment
    };
  }

  // حساب نطاق السعر
  private calculatePriceRange(candlesticks: CandlestickData[]): { min: number; max: number; range: number } {
    const prices = candlesticks.map(c => [c.open, c.high, c.low, c.close]).flat();
    const min = Math.min(...prices);
    const max = Math.max(...prices);
    return { min, max, range: max - min };
  }

  // حساب متوسط الحجم
  private calculateAverageVolume(candlesticks: CandlestickData[]): number {
    return candlesticks.reduce((sum, c) => sum + c.volume, 0) / candlesticks.length;
  }

  // حساب التقلبات
  private calculateVolatility(candlesticks: CandlestickData[]): number {
    const returns = [];
    for (let i = 1; i < candlesticks.length; i++) {
      const ret = (candlesticks[i].close - candlesticks[i - 1].close) / candlesticks[i - 1].close;
      returns.push(ret);
    }
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  }

  // تنزيل الملف
  downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  // تصدير سريع CSV
  async quickExportCSV(marketData: MarketData, patterns: DetectedPattern[], symbol: string): Promise<void> {
    const options: ExportOptions = {
      format: 'csv',
      includePatterns: true,
      includeIndicators: true,
      includeStatistics: true,
      includeChartData: true,
      language: 'ar'
    };

    const csvContent = await this.exportCompleteData(marketData, patterns, options);
    const filename = `${symbol}_analysis_${new Date().toISOString().split('T')[0]}.csv`;
    this.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
  }

  // تصدير سريع JSON
  async quickExportJSON(marketData: MarketData, patterns: DetectedPattern[], symbol: string): Promise<void> {
    const options: ExportOptions = {
      format: 'json',
      includePatterns: true,
      includeIndicators: true,
      includeStatistics: true,
      includeChartData: true,
      language: 'ar'
    };

    const jsonContent = await this.exportCompleteData(marketData, patterns, options);
    const filename = `${symbol}_analysis_${new Date().toISOString().split('T')[0]}.json`;
    this.downloadFile(jsonContent, filename, 'application/json;charset=utf-8;');
  }
}

export const exportService = new ExportService();
