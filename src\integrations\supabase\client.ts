// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://uarpckvcljnpvqkfrgrq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVhcnBja3ZjbGpucHZxa2ZyZ3JxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2ODY4NDEsImV4cCI6MjA2NDI2Mjg0MX0._XywtLWsee5bojk32HoetriBDgJ_93kW5a9fmHe_K7s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);