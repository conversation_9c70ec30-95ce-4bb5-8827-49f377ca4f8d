import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity, 
  Brain,
  Target,
  Zap,
  Shield,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>riangle,
  Timer,
  Award
} from 'lucide-react';
import { Candlestick<PERSON>hart } from '@/components/advanced-charts/CandlestickChart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface DashboardProps {
  lang: 'en' | 'ar';
}

const Dashboard = ({ lang = 'ar' }: DashboardProps) => {
  const [selectedSymbol, setSelectedSymbol] = useState('BTC/USDT');
  const [activeTab, setActiveTab] = useState('overview');
  const [isRealTime, setIsRealTime] = useState(true);
  const [portfolioValue, setPortfolioValue] = useState(125430);
  const [dailyProfit, setDailyProfit] = useState(1245);
  const [successRate, setSuccessRate] = useState(87.3);
  const [activeTrades, setActiveTrades] = useState(24);
  
  // Real-time updates simulation
  useEffect(() => {
    if (!isRealTime) return;
    
    const interval = setInterval(() => {
      // Simulate real-time data updates
      setPortfolioValue(prev => prev + (Math.random() - 0.5) * 1000);
      setDailyProfit(prev => prev + (Math.random() - 0.5) * 50);
      setSuccessRate(prev => Math.max(70, Math.min(95, prev + (Math.random() - 0.5) * 2)));
    }, 3000);

    return () => clearInterval(interval);
  }, [isRealTime]);

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
              {lang === 'ar' ? 'لوحة التحكم الذكية' : 'AI Trading Dashboard'}
            </h1>
            <p className="text-gray-400">
              {lang === 'ar' ? 'منصة التداول المدعومة بالذكاء الاصطناعي' : 'AI-Powered Trading Platform'}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant={isRealTime ? "default" : "secondary"} className="px-3 py-1">
              <div className={`w-2 h-2 rounded-full mr-2 ${isRealTime ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`} />
              {lang === 'ar' ? (isRealTime ? 'مباشر' : 'متوقف') : (isRealTime ? 'Live' : 'Paused')}
            </Badge>
            <Button
              variant={isRealTime ? "destructive" : "default"}
              size="sm"
              onClick={() => setIsRealTime(!isRealTime)}
            >
              {lang === 'ar' ? (isRealTime ? 'إيقاف' : 'تشغيل') : (isRealTime ? 'Pause' : 'Start')}
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800 border-slate-700 hover:border-green-500 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">
                    {lang === 'ar' ? 'إجمالي المحفظة' : 'Total Portfolio'}
                  </p>
                  <p className="text-3xl font-bold text-green-400">
                    ${portfolioValue.toLocaleString()}
                  </p>
                  <p className="text-sm text-green-300">
                    +12.5% {lang === 'ar' ? 'هذا الأسبوع' : 'this week'}
                  </p>
                </div>
                <div className="p-3 bg-green-500/20 rounded-full">
                  <DollarSign className="h-8 w-8 text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700 hover:border-blue-500 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">
                    {lang === 'ar' ? 'الربح اليومي' : 'Daily Profit'}
                  </p>
                  <p className="text-3xl font-bold text-blue-400">
                    +${dailyProfit.toLocaleString()}
                  </p>
                  <p className="text-sm text-blue-300">
                    +3.2% {lang === 'ar' ? 'من أمس' : 'from yesterday'}
                  </p>
                </div>
                <div className="p-3 bg-blue-500/20 rounded-full">
                  <TrendingUp className="h-8 w-8 text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700 hover:border-purple-500 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">
                    {lang === 'ar' ? 'الصفقات النشطة' : 'Active Trades'}
                  </p>
                  <p className="text-3xl font-bold text-purple-400">{activeTrades}</p>
                  <p className="text-sm text-purple-300">
                    156 {lang === 'ar' ? 'إجمالي' : 'total'}
                  </p>
                </div>
                <div className="p-3 bg-purple-500/20 rounded-full">
                  <Activity className="h-8 w-8 text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700 hover:border-yellow-500 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400 mb-1">
                    {lang === 'ar' ? 'معدل النجاح' : 'Success Rate'}
                  </p>
                  <p className="text-3xl font-bold text-yellow-400">{successRate.toFixed(1)}%</p>
                  <p className="text-sm text-yellow-300">
                    {lang === 'ar' ? 'أعلى من المتوسط' : 'Above average'}
                  </p>
                </div>
                <div className="p-3 bg-yellow-500/20 rounded-full">
                  <Award className="h-8 w-8 text-yellow-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Symbol Selector */}
        <Card className="bg-slate-800 border-slate-700 mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-400" />
              {lang === 'ar' ? 'اختيار الرمز' : 'Symbol Selection'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT'].map(symbol => (
                <Button
                  key={symbol}
                  variant={selectedSymbol === symbol ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedSymbol(symbol)}
                  className="transition-all duration-200"
                >
                  {symbol}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
        
        {/* Main Analysis Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 bg-slate-800 p-2 rounded-lg mb-6 h-auto">
            <TabsTrigger value="overview" className="flex items-center gap-2 p-3">
              <BarChart3 className="h-4 w-4" />
              {lang === 'ar' ? 'نظرة عامة' : 'Overview'}
            </TabsTrigger>
            <TabsTrigger value="charts" className="flex items-center gap-2 p-3">
              <Activity className="h-4 w-4" />
              {lang === 'ar' ? 'الرسوم البيانية' : 'Charts'}
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2 p-3">
              <Brain className="h-4 w-4" />
              {lang === 'ar' ? 'الذكاء الاصطناعي' : 'AI Analysis'}
            </TabsTrigger>
            <TabsTrigger value="signals" className="flex items-center gap-2 p-3">
              <Target className="h-4 w-4" />
              {lang === 'ar' ? 'الإشارات' : 'Signals'}
            </TabsTrigger>
            <TabsTrigger value="risk" className="flex items-center gap-2 p-3">
              <Shield className="h-4 w-4" />
              {lang === 'ar' ? 'إدارة المخاطر' : 'Risk Management'}
            </TabsTrigger>
            <TabsTrigger value="portfolio" className="flex items-center gap-2 p-3">
              <PieChart className="h-4 w-4" />
              {lang === 'ar' ? 'المحفظة' : 'Portfolio'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Main Chart */}
              <div className="lg:col-span-2">
                <CandlestickChart 
                  symbol={selectedSymbol} 
                  timeframe="1H"
                  lang={lang} 
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="charts">
            <CandlestickChart 
              symbol={selectedSymbol} 
              timeframe="1H"
              lang={lang} 
            />
          </TabsContent>

          <TabsContent value="ai">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-400" />
                    {lang === 'ar' ? 'تنبؤات الذكاء الاصطناعي' : 'AI Predictions'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-900/20 border border-green-700 rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-green-400">
                          {lang === 'ar' ? 'اتجاه صعودي قوي' : 'Strong Bullish Trend'}
                        </span>
                        <Badge variant="default">92% {lang === 'ar' ? 'ثقة' : 'confidence'}</Badge>
                      </div>
                      <p className="text-sm text-gray-300">
                        {lang === 'ar' 
                          ? 'الذكاء الاصطناعي يتوقع ارتفاع السعر بـ 5.2% خلال الـ 24 ساعة القادمة'
                          : 'AI predicts 5.2% price increase in the next 24 hours'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-400" />
                    {lang === 'ar' ? 'التحليل السريع' : 'Quick Analysis'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-300">RSI</span>
                      <span className="text-blue-400 font-semibold">68.4</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">MACD</span>
                      <span className="text-green-400 font-semibold">+234.5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-300">{lang === 'ar' ? 'اتجاه السوق' : 'Market Trend'}</span>
                      <Badge variant="default">
                        {lang === 'ar' ? 'صعودي' : 'Bullish'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="signals">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-green-400" />
                    {lang === 'ar' ? 'إشارة شراء' : 'Buy Signal'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-900/20 border border-green-700 rounded">
                      <p className="font-medium text-green-400">BTC/USDT</p>
                      <p className="text-sm text-gray-300">
                        {lang === 'ar' ? 'نقطة دخول: $45,100' : 'Entry: $45,100'}
                      </p>
                      <p className="text-sm text-gray-300">
                        {lang === 'ar' ? 'هدف: $47,500' : 'Target: $47,500'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    {lang === 'ar' ? 'تحذيرات' : 'Alerts'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="p-2 bg-yellow-900/20 border border-yellow-700 rounded text-sm">
                      {lang === 'ar' ? 'مقاومة قوية عند $46,000' : 'Strong resistance at $46,000'}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Timer className="h-5 w-5 text-blue-400" />
                    {lang === 'ar' ? 'الصفقات الأخيرة' : 'Recent Trades'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>BTC/USDT</span>
                      <span className="text-green-400">+2.3%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>ETH/USDT</span>
                      <span className="text-red-400">-1.1%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="risk">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-blue-400" />
                    {lang === 'ar' ? 'تقييم المخاطر' : 'Risk Assessment'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>{lang === 'ar' ? 'مستوى المخاطر' : 'Risk Level'}</span>
                      <Badge variant="secondary">{lang === 'ar' ? 'متوسط' : 'Medium'}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>{lang === 'ar' ? 'تقلبات السوق' : 'Market Volatility'}</span>
                      <span className="text-yellow-400">15.3%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-gray-400" />
                    {lang === 'ar' ? 'إعدادات المخاطر' : 'Risk Settings'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm text-gray-400">
                        {lang === 'ar' ? 'الحد الأقصى للمخاطرة' : 'Max Risk per Trade'}
                      </label>
                      <p className="text-white">2%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="portfolio">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle>{lang === 'ar' ? 'توزيع المحفظة' : 'Portfolio Distribution'}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>BTC</span>
                      <span className="text-orange-400">45%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>ETH</span>
                      <span className="text-blue-400">30%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Others</span>
                      <span className="text-green-400">25%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle>{lang === 'ar' ? 'الأداء' : 'Performance'}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>{lang === 'ar' ? 'هذا الشهر' : 'This Month'}</span>
                      <span className="text-green-400">+8.7%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{lang === 'ar' ? 'هذا العام' : 'This Year'}</span>
                      <span className="text-green-400">+45.2%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle>{lang === 'ar' ? 'المقاييس' : 'Metrics'}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Sharpe Ratio</span>
                      <span className="text-blue-400">2.34</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Drawdown</span>
                      <span className="text-red-400">-8.1%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 hover:border-green-500 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-300">إجمالي الاستثمارات</h3>
                <p className="text-2xl font-bold text-green-400">$125,430</p>
                <p className="text-sm text-green-300">+12.5% هذا الأسبوع</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 hover:border-blue-500 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-300">الربح اليومي</h3>
                <p className="text-2xl font-bold text-blue-400">+$1,245</p>
                <p className="text-sm text-blue-300">+3.2% من أمس</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 hover:border-purple-500 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-300">عدد الصفقات</h3>
                <p className="text-2xl font-bold text-purple-400">156</p>
                <p className="text-sm text-purple-300">24 نشطة الآن</p>
              </div>
              <Activity className="h-8 w-8 text-purple-400" />
            </div>
          </div>
          
          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700 hover:border-yellow-500 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-300">معدل النجاح</h3>
                <p className="text-2xl font-bold text-yellow-400">87.3%</p>
                <p className="text-sm text-yellow-300">أعلى من المتوسط</p>
              </div>
              <TrendingUp className="h-8 w-8 text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* ويدجت المشاعر المباشرة */}
          <div>
            <RealTimeSentimentWidget marketSentiment={marketSentiment} lang="ar" />
          </div>

          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h2 className="text-xl font-semibold mb-4 text-white">الأداء الأخير</h2>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-slate-700 rounded">
                <div>
                  <span className="text-white font-medium">AAPL</span>
                  <p className="text-sm text-gray-400">Apple Inc.</p>
                </div>
                <div className="text-right">
                  <span className="text-green-400 font-bold">+5.2%</span>
                  <p className="text-sm text-gray-400">$150.25</p>
                </div>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-700 rounded">
                <div>
                  <span className="text-white font-medium">TSLA</span>
                  <p className="text-sm text-gray-400">Tesla Inc.</p>
                </div>
                <div className="text-right">
                  <span className="text-red-400 font-bold">-2.1%</span>
                  <p className="text-sm text-gray-400">$245.80</p>
                </div>
              </div>
              <div className="flex justify-between items-center p-3 bg-slate-700 rounded">
                <div>
                  <span className="text-white font-medium">BTC</span>
                  <p className="text-sm text-gray-400">Bitcoin</p>
                </div>
                <div className="text-right">
                  <span className="text-green-400 font-bold">+8.7%</span>
                  <p className="text-sm text-gray-400">$45,230</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
            <h2 className="text-xl font-semibold mb-4 text-white">التحليلات السريعة</h2>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-300">RSI المتوسط</span>
                <span className="text-blue-400 font-semibold">65.2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">اتجاه السوق</span>
                <span className="text-green-400 font-semibold">صاعد</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">مؤشر الخوف والطمع</span>
                <span className="text-yellow-400 font-semibold">72 - طمع</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">حجم التداول</span>
                <span className="text-purple-400 font-semibold">عالي</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-slate-800 p-6 rounded-lg border border-slate-700">
          <h2 className="text-xl font-semibold mb-4 text-white">الأخبار العاجلة</h2>
          <div className="space-y-3">
            <div className="p-3 bg-slate-700 rounded border-l-4 border-green-500">
              <p className="text-white font-medium">ارتفاع مؤشرات الأسهم الأمريكية</p>
              <p className="text-sm text-gray-400">منذ 15 دقيقة</p>
            </div>
            <div className="p-3 bg-slate-700 rounded border-l-4 border-blue-500">
              <p className="text-white font-medium">إعلان نتائج أرباح شركة Apple</p>
              <p className="text-sm text-gray-400">منذ ساعة</p>
            </div>
            <div className="p-3 bg-slate-700 rounded border-l-4 border-yellow-500">
              <p className="text-white font-medium">تحديث على أسعار النفط</p>
              <p className="text-sm text-gray-400">منذ ساعتين</p>
            </div>
          </div>
        </div>
      </div>
      {/* سوق ثلاثي الأبعاد */}
      <Market3DView />
    </div>
  );
};

export default Dashboard;
