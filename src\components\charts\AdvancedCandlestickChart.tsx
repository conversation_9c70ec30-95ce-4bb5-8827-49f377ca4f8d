import React, { useEffect, useRef, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  TooltipItem,
  BarElement,
  LineElement,
  PointElement
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import 'chartjs-adapter-date-fns';
import { CandlestickData, TechnicalIndicator } from '@/services/enhancedMarketDataService';

// تسجيل المكونات المطلوبة
ChartJS.register(
  CategoryScale,
  LinearScale,
  TimeScale,
  Title,
  Tooltip,
  Legend,
  BarElement,
  LineElement,
  PointElement
);

interface AdvancedCandlestickChartProps {
  data: CandlestickData[];
  indicators?: TechnicalIndicator[];
  supportLevels?: number[];
  resistanceLevels?: number[];
  trendLines?: Array<{
    start: { x: number; y: number };
    end: { x: number; y: number };
    type: 'support' | 'resistance' | 'trend';
  }>;
  symbol: string;
  timeframe: string;
  height?: number;
  showVolume?: boolean;
  showIndicators?: boolean;
  theme?: 'light' | 'dark';
}

const AdvancedCandlestickChart: React.FC<AdvancedCandlestickChartProps> = ({
  data,
  indicators = [],
  supportLevels = [],
  resistanceLevels = [],
  trendLines = [],
  symbol,
  timeframe,
  height = 600,
  showVolume = true,
  showIndicators = true,
  theme = 'dark'
}) => {
  const chartRef = useRef<ChartJS>(null);
  const [chartData, setChartData] = useState<any>(null);
  const [chartOptions, setChartOptions] = useState<ChartOptions<'line'>>();

  useEffect(() => {
    if (data && data.length > 0) {
      prepareSimpleCandlestickData();
      prepareChartOptions();
    }
  }, [data, indicators, supportLevels, resistanceLevels, showVolume, showIndicators, theme]);

  const prepareSimpleCandlestickData = () => {
    const datasets: any[] = [];

    // بيانات الأسعار كخطوط
    const priceData = data.map((candle, index) => ({
      x: index,
      y: candle.close
    }));

    const highData = data.map((candle, index) => ({
      x: index,
      y: candle.high
    }));

    const lowData = data.map((candle, index) => ({
      x: index,
      y: candle.low
    }));

    // خط الإغلاق الرئيسي
    datasets.push({
      label: `${symbol} - Close`,
      data: priceData,
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      borderWidth: 2,
      fill: false,
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 4,
      yAxisID: 'y'
    });

    // خط الأعلى
    datasets.push({
      label: `${symbol} - High`,
      data: highData,
      borderColor: '#10B981',
      backgroundColor: 'transparent',
      borderWidth: 1,
      fill: false,
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 2,
      yAxisID: 'y',
      borderDash: [2, 2]
    });

    // خط الأقل
    datasets.push({
      label: `${symbol} - Low`,
      data: lowData,
      borderColor: '#EF4444',
      backgroundColor: 'transparent',
      borderWidth: 1,
      fill: false,
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 2,
      yAxisID: 'y',
      borderDash: [2, 2]
    });

    // إضافة المؤشرات الفنية
    if (showIndicators && indicators.length > 0) {
      indicators.forEach((indicator, index) => {
        if (indicator.type === 'line' && indicator.values.length > 0) {
          datasets.push({
            label: indicator.name,
            data: indicator.values.map((value, i) => ({
              x: i,
              y: value
            })).filter(point => !isNaN(point.y)),
            borderColor: indicator.color,
            backgroundColor: 'transparent',
            borderWidth: 1.5,
            pointRadius: 0,
            pointHoverRadius: 3,
            yAxisID: indicator.name.includes('RSI') ? 'y1' : 'y',
            tension: 0.1,
            fill: false
          });
        }
      });
    }

    // إضافة مستويات الدعم والمقاومة
    if (supportLevels.length > 0) {
      supportLevels.forEach((level, index) => {
        datasets.push({
          label: `Support ${index + 1}`,
          data: [
            { x: 0, y: level },
            { x: data.length - 1, y: level }
          ],
          borderColor: '#10B981',
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderDash: [5, 5],
          pointRadius: 0,
          yAxisID: 'y',
          fill: false
        });
      });
    }

    if (resistanceLevels.length > 0) {
      resistanceLevels.forEach((level, index) => {
        datasets.push({
          label: `Resistance ${index + 1}`,
          data: [
            { x: 0, y: level },
            { x: data.length - 1, y: level }
          ],
          borderColor: '#EF4444',
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderDash: [5, 5],
          pointRadius: 0,
          yAxisID: 'y',
          fill: false
        });
      });
    }

    setChartData({
      datasets
    });
  };

  const prepareChartOptions = () => {
    const isDark = theme === 'dark';
    const textColor = isDark ? '#F3F4F6' : '#1F2937';
    const gridColor = isDark ? '#374151' : '#E5E7EB';
    const backgroundColor = isDark ? '#111827' : '#FFFFFF';

    const options: ChartOptions<'line'> = {
      responsive: true,
      maintainAspectRatio: false,
      backgroundColor,
      plugins: {
        title: {
          display: true,
          text: `${symbol} - ${timeframe}`,
          color: textColor,
          font: {
            size: 18,
            weight: 'bold'
          }
        },
        legend: {
          display: true,
          position: 'top',
          labels: {
            color: textColor,
            usePointStyle: true,
            filter: (legendItem) => {
              // إخفاء مستويات الدعم والمقاومة من الأسطورة
              return !legendItem.text?.includes('Support') &&
                     !legendItem.text?.includes('Resistance');
            }
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          backgroundColor: isDark ? 'rgba(17, 24, 39, 0.95)' : 'rgba(255, 255, 255, 0.95)',
          titleColor: textColor,
          bodyColor: textColor,
          borderColor: gridColor,
          borderWidth: 1,
          callbacks: {
            title: (context) => {
              const index = context[0].dataIndex;
              if (data[index]) {
                const date = new Date(data[index].timestamp);
                return date.toLocaleString('ar-SA', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                });
              }
              return '';
            },
            label: (context: TooltipItem<'line'>) => {
              const datasetLabel = context.dataset.label || '';
              const index = context.dataIndex;

              if (datasetLabel.includes('Close') && data[index]) {
                const candle = data[index];
                return [
                  `فتح: ${candle.open?.toFixed(5)}`,
                  `أعلى: ${candle.high?.toFixed(5)}`,
                  `أقل: ${candle.low?.toFixed(5)}`,
                  `إغلاق: ${candle.close?.toFixed(5)}`,
                  `الحجم: ${(candle.volume / 1000000).toFixed(2)}M`
                ];
              } else {
                return `${datasetLabel}: ${context.parsed.y?.toFixed(2)}`;
              }
            }
          }
        }
      },
      scales: {
        x: {
          type: 'linear',
          grid: {
            color: gridColor,
            drawOnChartArea: true
          },
          ticks: {
            color: textColor,
            maxTicksLimit: 10,
            callback: function(value, index) {
              if (data[index]) {
                const date = new Date(data[index].timestamp);
                return date.toLocaleDateString('ar-SA', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit'
                });
              }
              return '';
            }
          },
          title: {
            display: true,
            text: 'الوقت',
            color: textColor
          }
        },
        y: {
          type: 'linear',
          position: 'right',
          grid: {
            color: gridColor,
            drawOnChartArea: true
          },
          ticks: {
            color: textColor,
            callback: function(value) {
              return (value as number).toFixed(5);
            }
          },
          title: {
            display: true,
            text: 'السعر',
            color: textColor
          }
        },
        y1: {
          type: 'linear',
          position: 'left',
          min: 0,
          max: 100,
          grid: {
            drawOnChartArea: false
          },
          ticks: {
            color: textColor,
            display: false
          },
          title: {
            display: false
          }
        }
      },
      interaction: {
        mode: 'index',
        intersect: false
      },
      elements: {
        point: {
          radius: 0,
          hoverRadius: 4
        },
        line: {
          tension: 0.1
        }
      }
    };

    setChartOptions(options);
  };

  if (!chartData || !chartOptions) {
    return (
      <div className="flex items-center justify-center h-96 bg-slate-800 rounded-lg">
        <div className="text-white">جاري تحميل الرسم البياني...</div>
      </div>
    );
  }

  return (
    <div className="w-full" style={{ height: `${height}px` }}>
      <Line
        ref={chartRef}
        data={chartData}
        options={chartOptions}
      />
    </div>
  );
};

export default AdvancedCandlestickChart;
