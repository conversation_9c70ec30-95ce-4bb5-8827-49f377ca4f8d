import React, { useState, useEffect } from 'react';
import { MessageCircle, TrendingUp, Users, AlertTriangle, Target, Activity, Globe } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { socialSentimentService, MarketSentiment } from '@/services/socialSentimentService';

interface SocialSentimentAnalysisProps {
  lang?: 'en' | 'ar';
}

const SocialSentimentAnalysis: React.FC<SocialSentimentAnalysisProps> = ({ lang = 'ar' }) => {
  const [marketSentiment, setMarketSentiment] = useState<MarketSentiment | null>(null);
  const [selectedSymbol, setSelectedSymbol] = useState('BTCUSDT');
  const [isLoading, setIsLoading] = useState(false);

  const symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOGEUSDT', 'EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'];

  useEffect(() => {
    loadSentimentData();
  }, [selectedSymbol]);

  const loadSentimentData = async () => {
    setIsLoading(true);
    try {
      const sentiment = await socialSentimentService.analyzeSentiment(selectedSymbol);
      setMarketSentiment(sentiment);
    } catch (error) {
      console.error('Error loading sentiment data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSentimentColor = (score: number) => {
    if (score > 0.3) return 'text-green-400';
    if (score > 0.1) return 'text-green-300';
    if (score < -0.3) return 'text-red-400';
    if (score < -0.1) return 'text-red-300';
    return 'text-yellow-400';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4 flex items-center justify-center gap-2">
            <Globe className="h-8 w-8 text-blue-400" />
            {lang === 'ar' ? 'تحليل المشاعر من وسائل التواصل الاجتماعي' : 'Social Media Sentiment Analysis'}
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            {lang === 'ar'
              ? 'تحليل متقدم للمشاعر من Twitter و Reddit مع مؤشرات الخوف والطمع'
              : 'Advanced sentiment analysis from Twitter and Reddit with fear & greed indicators'
            }
          </p>
        </div>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'الرمز' : 'Symbol'}
                </label>
                <Select value={selectedSymbol} onValueChange={setSelectedSymbol}>
                  <SelectTrigger className="bg-slate-700 border-slate-600">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {symbols.map(symbol => (
                      <SelectItem key={symbol} value={symbol}>{symbol}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-400">
                  {lang === 'ar' ? 'تحديث البيانات' : 'Refresh Data'}
                </label>
                <Button
                  onClick={loadSentimentData}
                  disabled={isLoading}
                  className="w-full flex items-center gap-2"
                >
                  <Activity className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  {isLoading
                    ? (lang === 'ar' ? 'جاري التحديث...' : 'Updating...')
                    : (lang === 'ar' ? 'تحديث' : 'Refresh')
                  }
                </Button>
              </div>

              {marketSentiment && (
                <div className="space-y-1">
                  <div className="text-sm text-gray-400">{lang === 'ar' ? 'مؤشر الخوف والطمع:' : 'Fear & Greed:'}</div>
                  <div className="text-lg font-bold text-purple-400">
                    {marketSentiment.signals.fearGreedIndex}/100
                  </div>
                  <Progress value={marketSentiment.signals.fearGreedIndex} className="h-2" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {marketSentiment && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <MessageCircle className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'حجم التفاعل' : 'Social Volume'}
                </h3>
                <p className="text-2xl font-bold text-blue-400">
                  {formatNumber(marketSentiment.overall.volume)}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <TrendingUp className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'الانتشار الفيروسي' : 'Virality Score'}
                </h3>
                <p className="text-2xl font-bold text-green-400">
                  {marketSentiment.signals.viralityScore}/100
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <AlertTriangle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'نقاط الجدل' : 'Controversy'}
                </h3>
                <p className="text-2xl font-bold text-yellow-400">
                  {marketSentiment.signals.controversyScore}/100
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <Target className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'مخاطر التلاعب' : 'Manipulation Risk'}
                </h3>
                <p className="text-2xl font-bold text-red-400">
                  {marketSentiment.signals.manipulationRisk}/100
                </p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {lang === 'ar' ? 'المؤثرون' : 'Influencers'}
                </h3>
                <p className="text-2xl font-bold text-purple-400">
                  {marketSentiment.influencers.length}
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {marketSentiment && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-400" />
                {lang === 'ar' ? 'مؤشر المشاعر الرئيسي' : 'Main Sentiment Indicator'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-6xl font-bold mb-2">
                    <span className={getSentimentColor(marketSentiment.overall.score)}>
                      {(marketSentiment.overall.score * 100).toFixed(0)}%
                    </span>
                  </div>
                  <div className="text-lg text-gray-400 mb-4">
                    {lang === 'ar' ? 'نقاط المشاعر' : 'Sentiment Score'}
                  </div>
                  <Progress
                    value={((marketSentiment.overall.score + 1) / 2) * 100}
                    className="h-4 mb-4"
                  />
                  <Badge className="text-lg px-4 py-2">
                    {marketSentiment.overall.trend} - {marketSentiment.overall.strength}
                  </Badge>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">{lang === 'ar' ? 'الخوف والطمع:' : 'Fear & Greed:'}</span>
                      <span className="text-purple-400 font-bold">
                        {marketSentiment.signals.fearGreedIndex}/100
                      </span>
                    </div>
                    <Progress value={marketSentiment.signals.fearGreedIndex} className="h-3" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">{lang === 'ar' ? 'الحجم الاجتماعي:' : 'Social Volume:'}</span>
                      <span className="text-blue-400 font-bold">
                        {marketSentiment.signals.socialVolume}/100
                      </span>
                    </div>
                    <Progress value={marketSentiment.signals.socialVolume} className="h-3" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">{lang === 'ar' ? 'الانتشار الفيروسي:' : 'Virality:'}</span>
                      <span className="text-green-400 font-bold">
                        {marketSentiment.signals.viralityScore}/100
                      </span>
                    </div>
                    <Progress value={marketSentiment.signals.viralityScore} className="h-3" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">{lang === 'ar' ? 'مستوى الثقة:' : 'Confidence Level:'}</span>
                      <span className="text-white font-bold">
                        {(marketSentiment.overall.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                    <Progress value={marketSentiment.overall.confidence * 100} className="h-2" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">🐦</span>
                Twitter
                <Badge variant="outline" className="ml-auto">
                  847 posts
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-400">{lang === 'ar' ? 'نقاط المشاعر:' : 'Sentiment Score:'}</span>
                    <span className="font-bold text-green-400">
                      72%
                    </span>
                  </div>
                  <Progress
                    value={72}
                    className="h-3"
                  />
                </div>
                <div className="text-center p-3 bg-slate-700 rounded-lg">
                  <div className="text-lg font-bold text-white">
                    {formatNumber(15420)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {lang === 'ar' ? 'تفاعل' : 'Engagement'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">📱</span>
                Reddit
                <Badge variant="outline" className="ml-auto">
                  400 posts
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-400">{lang === 'ar' ? 'نقاط المشاعر:' : 'Sentiment Score:'}</span>
                    <span className="font-bold text-green-300">
                      58%
                    </span>
                  </div>
                  <Progress
                    value={58}
                    className="h-3"
                  />
                </div>
                <div className="text-center p-3 bg-slate-700 rounded-lg">
                  <div className="text-lg font-bold text-white">
                    {formatNumber(8950)}
                  </div>
                  <div className="text-xs text-gray-400">
                    {lang === 'ar' ? 'تفاعل' : 'Engagement'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* معلومات إضافية */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-400" />
                {lang === 'ar' ? 'الاتجاهات الشائعة' : 'Trending Topics'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-2 bg-slate-700 rounded">
                  <span className="text-sm">#Bitcoin</span>
                  <Badge variant="outline" className="text-green-400">+15%</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-slate-700 rounded">
                  <span className="text-sm">#Ethereum</span>
                  <Badge variant="outline" className="text-blue-400">+8%</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-slate-700 rounded">
                  <span className="text-sm">#DeFi</span>
                  <Badge variant="outline" className="text-yellow-400">+5%</Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-slate-700 rounded">
                  <span className="text-sm">#NFT</span>
                  <Badge variant="outline" className="text-red-400">-3%</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-purple-400" />
                {lang === 'ar' ? 'أهم المؤثرين' : 'Top Influencers'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-2 bg-slate-700 rounded">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">🐦</span>
                    <div>
                      <div className="text-sm font-medium">CryptoExpert</div>
                      <div className="text-xs text-gray-400">
                        125K {lang === 'ar' ? 'متابع' : 'followers'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-green-400">
                      80%
                    </div>
                    <Badge variant="outline" className="text-xs text-blue-400">✓</Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between p-2 bg-slate-700 rounded">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">📱</span>
                    <div>
                      <div className="text-sm font-medium">BlockchainGuru</div>
                      <div className="text-xs text-gray-400">
                        89K {lang === 'ar' ? 'متابع' : 'followers'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-green-300">
                      60%
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-400" />
                {lang === 'ar' ? 'تحذيرات السوق' : 'Market Alerts'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-yellow-900/20 border border-yellow-700 rounded">
                  <div className="flex items-center gap-2 mb-1">
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                    <span className="text-sm font-medium text-yellow-400">
                      {lang === 'ar' ? 'تقلبات عالية' : 'High Volatility'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400">
                    {lang === 'ar'
                      ? 'مستوى تقلبات مرتفع في المشاعر خلال الساعة الماضية'
                      : 'High sentiment volatility detected in the last hour'
                    }
                  </p>
                </div>

                <div className="p-3 bg-blue-900/20 border border-blue-700 rounded">
                  <div className="flex items-center gap-2 mb-1">
                    <TrendingUp className="h-4 w-4 text-blue-400" />
                    <span className="text-sm font-medium text-blue-400">
                      {lang === 'ar' ? 'اتجاه صاعد' : 'Bullish Trend'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400">
                    {lang === 'ar'
                      ? 'المشاعر الإيجابية في ازدياد مستمر'
                      : 'Positive sentiment showing consistent growth'
                    }
                  </p>
                </div>

                <div className="p-3 bg-green-900/20 border border-green-700 rounded">
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="h-4 w-4 text-green-400" />
                    <span className="text-sm font-medium text-green-400">
                      {lang === 'ar' ? 'فرصة شراء' : 'Buy Opportunity'}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400">
                    {lang === 'ar'
                      ? 'المؤشرات تشير إلى فرصة شراء محتملة'
                      : 'Indicators suggest potential buying opportunity'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* إحصائيات الوقت الفعلي */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-400" />
              {lang === 'ar' ? 'إحصائيات الوقت الفعلي' : 'Real-Time Statistics'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">
                  1.2K
                </div>
                <div className="text-sm text-gray-400">
                  {lang === 'ar' ? 'إجمالي المنشورات' : 'Total Posts'}
                </div>
                <div className="text-xs text-green-400 mt-1">
                  +25 {lang === 'ar' ? 'في الساعة الأخيرة' : 'last hour'}
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">
                  82%
                </div>
                <div className="text-sm text-gray-400">
                  {lang === 'ar' ? 'مستوى الثقة' : 'Confidence Level'}
                </div>
                <div className="text-xs text-blue-400 mt-1">
                  {lang === 'ar' ? 'عالي' : 'High'}
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  2
                </div>
                <div className="text-sm text-gray-400">
                  {lang === 'ar' ? 'مؤثرون نشطون' : 'Active Influencers'}
                </div>
                <div className="text-xs text-yellow-400 mt-1">
                  {lang === 'ar' ? 'موثقون' : 'Verified'}: 1
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-400 mb-2">
                  {new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}
                </div>
                <div className="text-sm text-gray-400">
                  {lang === 'ar' ? 'آخر تحديث' : 'Last Update'}
                </div>
                <div className="text-xs text-green-400 mt-1">
                  {lang === 'ar' ? 'مباشر' : 'Live'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {!marketSentiment && (
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-12 text-center">
              <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4 animate-pulse" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {lang === 'ar' ? 'جاري تحميل بيانات المشاعر' : 'Loading Sentiment Data'}
              </h3>
              <p className="text-gray-400">
                {lang === 'ar' ? 'يرجى الانتظار...' : 'Please wait...'}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SocialSentimentAnalysis;
