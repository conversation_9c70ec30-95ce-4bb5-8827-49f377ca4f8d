import React, { useState, useEffect } from 'react';
import { Activity, TrendingUp, TrendingDown, AlertTriangle, Users, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { MarketSentiment } from '@/services/socialSentimentService';

interface RealTimeSentimentWidgetProps {
  marketSentiment: MarketSentiment | null;
  lang?: 'en' | 'ar';
  compact?: boolean;
}

const RealTimeSentimentWidget: React.FC<RealTimeSentimentWidgetProps> = ({
  marketSentiment,
  lang = 'ar',
  compact = false
}) => {
  const [isLive, setIsLive] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(Date.now());
    }, 5000); // تحديث كل 5 ثوان

    return () => clearInterval(interval);
  }, []);

  const getSentimentColor = (score: number) => {
    if (score > 0.3) return 'text-green-400';
    if (score > 0.1) return 'text-green-300';
    if (score < -0.3) return 'text-red-400';
    if (score < -0.1) return 'text-red-300';
    return 'text-yellow-400';
  };

  const getSentimentBgColor = (score: number) => {
    if (score > 0.3) return 'bg-green-900/20';
    if (score > 0.1) return 'bg-green-900/10';
    if (score < -0.3) return 'bg-red-900/20';
    if (score < -0.1) return 'bg-red-900/10';
    return 'bg-yellow-900/20';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'bullish': return <TrendingUp className="h-4 w-4 text-green-400" />;
      case 'bearish': return <TrendingDown className="h-4 w-4 text-red-400" />;
      default: return <Activity className="h-4 w-4 text-yellow-400" />;
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return lang === 'ar' ? 'الآن' : 'now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return lang === 'ar' ? `منذ ${minutes} دقيقة` : `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return lang === 'ar' ? `منذ ${hours} ساعة` : `${hours}h ago`;
  };

  const getAlertLevel = (sentiment: MarketSentiment) => {
    const { manipulationRisk, controversyScore, viralityScore } = sentiment.signals;
    
    if (manipulationRisk > 70 || controversyScore > 80) return 'high';
    if (manipulationRisk > 40 || controversyScore > 60 || viralityScore > 80) return 'medium';
    return 'low';
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-400 bg-red-900/20';
      case 'medium': return 'text-yellow-400 bg-yellow-900/20';
      default: return 'text-green-400 bg-green-900/20';
    }
  };

  if (!marketSentiment) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6 text-center">
          <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-400">
            {lang === 'ar' ? 'جاري تحميل البيانات المباشرة...' : 'Loading live data...'}
          </p>
        </CardContent>
      </Card>
    );
  }

  const alertLevel = getAlertLevel(marketSentiment);

  if (compact) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
              <div>
                <div className={`text-lg font-bold ${getSentimentColor(marketSentiment.overall.score)}`}>
                  {(marketSentiment.overall.score * 100).toFixed(0)}%
                </div>
                <div className="text-xs text-gray-400">
                  {marketSentiment.overall.trend}
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-sm font-medium text-white">
                {marketSentiment.signals.fearGreedIndex}/100
              </div>
              <div className="text-xs text-gray-400">
                {lang === 'ar' ? 'خوف/طمع' : 'Fear/Greed'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
            <Zap className="h-5 w-5 text-blue-400" />
            {lang === 'ar' ? 'المشاعر المباشرة' : 'Live Sentiment'}
          </div>
          <Badge variant="outline" className="text-xs">
            {formatTimeAgo(marketSentiment.timestamp)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* المؤشر الرئيسي */}
        <div className="text-center">
          <div className={`text-4xl font-bold mb-2 ${getSentimentColor(marketSentiment.overall.score)}`}>
            {(marketSentiment.overall.score * 100).toFixed(0)}%
          </div>
          <div className="flex items-center justify-center gap-2 mb-4">
            {getTrendIcon(marketSentiment.overall.trend)}
            <span className="text-lg font-medium text-white">
              {marketSentiment.overall.trend}
            </span>
            <Badge className={getSentimentBgColor(marketSentiment.overall.score)}>
              {marketSentiment.overall.strength}
            </Badge>
          </div>
          <Progress 
            value={((marketSentiment.overall.score + 1) / 2) * 100} 
            className="h-3 mb-2" 
          />
          <div className="text-sm text-gray-400">
            {lang === 'ar' ? 'الثقة:' : 'Confidence:'} {(marketSentiment.overall.confidence * 100).toFixed(0)}%
          </div>
        </div>

        {/* المؤشرات السريعة */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-slate-700 rounded-lg">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              {marketSentiment.signals.fearGreedIndex}
            </div>
            <div className="text-xs text-gray-400">
              {lang === 'ar' ? 'مؤشر الخوف/الطمع' : 'Fear/Greed Index'}
            </div>
            <Progress value={marketSentiment.signals.fearGreedIndex} className="h-1 mt-2" />
          </div>

          <div className="text-center p-3 bg-slate-700 rounded-lg">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              {marketSentiment.overall.volume}
            </div>
            <div className="text-xs text-gray-400">
              {lang === 'ar' ? 'حجم التفاعل' : 'Social Volume'}
            </div>
            <div className="flex items-center justify-center gap-1 mt-1">
              <Users className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-400">
                {marketSentiment.influencers.length} {lang === 'ar' ? 'مؤثر' : 'influencers'}
              </span>
            </div>
          </div>
        </div>

        {/* تحذيرات */}
        <div className={`p-3 rounded-lg ${getAlertColor(alertLevel)}`}>
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">
              {lang === 'ar' ? 'مستوى التحذير:' : 'Alert Level:'} 
              {alertLevel === 'high' ? (lang === 'ar' ? ' عالي' : ' High') :
               alertLevel === 'medium' ? (lang === 'ar' ? ' متوسط' : ' Medium') :
               (lang === 'ar' ? ' منخفض' : ' Low')}
            </span>
          </div>
          
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div>
              <div className="font-medium">{lang === 'ar' ? 'التلاعب:' : 'Manipulation:'}</div>
              <div>{marketSentiment.signals.manipulationRisk}%</div>
            </div>
            <div>
              <div className="font-medium">{lang === 'ar' ? 'الجدل:' : 'Controversy:'}</div>
              <div>{marketSentiment.signals.controversyScore}%</div>
            </div>
            <div>
              <div className="font-medium">{lang === 'ar' ? 'الانتشار:' : 'Virality:'}</div>
              <div>{marketSentiment.signals.viralityScore}%</div>
            </div>
          </div>
        </div>

        {/* المنصات */}
        <div className="space-y-3">
          <h4 className="font-medium text-white">{lang === 'ar' ? 'المنصات:' : 'Platforms:'}</h4>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">🐦</span>
                <span className="text-sm">Twitter</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-sm font-bold ${getSentimentColor(marketSentiment.platforms.twitter.score)}`}>
                  {(marketSentiment.platforms.twitter.score * 100).toFixed(0)}%
                </span>
                <Badge variant="outline" className="text-xs">
                  {marketSentiment.platforms.twitter.volume}
                </Badge>
              </div>
            </div>
            <Progress 
              value={((marketSentiment.platforms.twitter.score + 1) / 2) * 100} 
              className="h-1" 
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">📱</span>
                <span className="text-sm">Reddit</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-sm font-bold ${getSentimentColor(marketSentiment.platforms.reddit.score)}`}>
                  {(marketSentiment.platforms.reddit.score * 100).toFixed(0)}%
                </span>
                <Badge variant="outline" className="text-xs">
                  {marketSentiment.platforms.reddit.volume}
                </Badge>
              </div>
            </div>
            <Progress 
              value={((marketSentiment.platforms.reddit.score + 1) / 2) * 100} 
              className="h-1" 
            />
          </div>
        </div>

        {/* آخر تحديث */}
        <div className="text-center text-xs text-gray-500 border-t border-slate-700 pt-3">
          {lang === 'ar' ? 'آخر تحديث:' : 'Last updated:'} {formatTimeAgo(lastUpdate)}
        </div>
      </CardContent>
    </Card>
  );
};

export default RealTimeSentimentWidget;
