{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@huggingface/transformers": "^3.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.56.2", "@tensorflow/tfjs": "^4.22.0", "@types/d3": "^7.4.3", "axios": "^1.9.0", "brain.js": "^2.0.0-beta.23", "ccxt": "^4.4.86", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-chart-financial": "^0.2.1", "chartjs-plugin-annotation": "^3.0.1", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "dexie": "^4.0.11", "embla-carousel-react": "^8.3.0", "ethers": "^6.14.3", "html2canvas": "^1.4.1", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "input-otp": "^1.2.4", "jspdf": "^2.5.2", "jsrsasign": "^11.1.0", "jstat": "^1.9.6", "lucide-react": "^0.462.0", "mathjs": "^11.12.0", "mqtt": "^5.3.4", "next-themes": "^0.3.0", "papaparse": "^5.5.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.53.0", "react-i18next": "^14.1.3", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "rxjs": "^7.8.2", "sonner": "^1.5.0", "speakeasy": "^2.0.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "technicalindicators": "^3.1.0", "three": "^0.160.1", "uuid": "^11.1.0", "vaul": "^0.9.3", "winston": "^3.17.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/react": "^14.1.2", "@types/node": "^22.5.5", "@types/papaparse": "^5.3.14", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.7", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.8", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}