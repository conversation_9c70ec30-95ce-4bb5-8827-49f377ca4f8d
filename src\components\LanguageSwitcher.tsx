import React from 'react';
import { Button } from '@/components/ui/button';
import { Globe, Languages } from 'lucide-react';

interface LanguageSwitcherProps {
  currentLang: 'en' | 'ar';
  setLang: (lang: 'en' | 'ar') => void;
}

export function LanguageSwitcher({ currentLang, setLang }: LanguageSwitcherProps) {
  return (
    <div className="flex items-center gap-2 bg-slate-700 rounded-lg p-1">
      <Globe className="h-4 w-4 text-blue-400 mx-2" />
      <Button
        variant={currentLang === 'ar' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setLang('ar')}
        className={`text-sm px-3 py-1 rounded-md transition-all duration-200 ${
          currentLang === 'ar' 
            ? 'bg-blue-600 text-white shadow-md' 
            : 'text-gray-300 hover:text-white hover:bg-slate-600'
        }`}
      >
        <Languages className="h-3 w-3 mr-1" />
        العربية
      </Button>
      <Button
        variant={currentLang === 'en' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setLang('en')}
        className={`text-sm px-3 py-1 rounded-md transition-all duration-200 ${
          currentLang === 'en' 
            ? 'bg-blue-600 text-white shadow-md' 
            : 'text-gray-300 hover:text-white hover:bg-slate-600'
        }`}
      >
        <Languages className="h-3 w-3 mr-1" />
        English
      </Button>
    </div>
  );
}
