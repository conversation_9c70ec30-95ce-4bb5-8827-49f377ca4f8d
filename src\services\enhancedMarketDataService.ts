// خدمة بيانات السوق المحسنة للشموع اليابانية
export interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  date: Date;
}

export interface TechnicalIndicator {
  name: string;
  values: number[];
  color: string;
  type: 'line' | 'area' | 'histogram';
}

export interface MarketData {
  symbol: string;
  timeframe: string;
  candlesticks: CandlestickData[];
  indicators: TechnicalIndicator[];
  supportLevels: number[];
  resistanceLevels: number[];
  trendLines: Array<{
    start: { x: number; y: number };
    end: { x: number; y: number };
    type: 'support' | 'resistance' | 'trend';
  }>;
}

export interface MarketPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  startIndex: number;
  endIndex: number;
  description: string;
}

class EnhancedMarketDataService {
  private symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'BTCUSDT', 'ETHUSDT', 'XAUUSD'];
  private timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d'];
  private cache: Map<string, MarketData> = new Map();

  constructor() {
    console.log('📊 Enhanced Market Data Service initialized');
  }

  // توليد بيانات الشموع اليابانية الواقعية
  generateCandlestickData(
    symbol: string, 
    timeframe: string, 
    count: number = 200
  ): CandlestickData[] {
    const data: CandlestickData[] = [];
    const basePrice = this.getBasePrice(symbol);
    const volatility = this.getVolatility(symbol);
    
    let currentPrice = basePrice;
    const now = new Date();
    const timeframeMs = this.getTimeframeMs(timeframe);

    for (let i = count - 1; i >= 0; i--) {
      const timestamp = now.getTime() - (i * timeframeMs);
      const date = new Date(timestamp);
      
      // محاكاة حركة السعر الواقعية
      const trend = this.calculateTrend(i, count);
      const noise = (Math.random() - 0.5) * volatility;
      const priceChange = trend + noise;
      
      const open = currentPrice;
      const close = open * (1 + priceChange);
      
      // حساب الأعلى والأدنى بناءً على التقلبات
      const wickRange = Math.abs(close - open) * (1 + Math.random());
      const high = Math.max(open, close) + (wickRange * Math.random());
      const low = Math.min(open, close) - (wickRange * Math.random());
      
      // حجم التداول
      const baseVolume = this.getBaseVolume(symbol);
      const volumeMultiplier = 0.5 + Math.random() * 1.5;
      const volume = baseVolume * volumeMultiplier;

      data.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume,
        date
      });

      currentPrice = close;
    }

    return data.reverse();
  }

  // حساب المؤشرات الفنية
  calculateTechnicalIndicators(candlesticks: CandlestickData[]): TechnicalIndicator[] {
    const indicators: TechnicalIndicator[] = [];

    // RSI
    const rsi = this.calculateRSI(candlesticks, 14);
    indicators.push({
      name: 'RSI',
      values: rsi,
      color: '#8B5CF6',
      type: 'line'
    });

    // MACD
    const macd = this.calculateMACD(candlesticks);
    indicators.push({
      name: 'MACD',
      values: macd.macd,
      color: '#3B82F6',
      type: 'line'
    });

    indicators.push({
      name: 'MACD Signal',
      values: macd.signal,
      color: '#EF4444',
      type: 'line'
    });

    indicators.push({
      name: 'MACD Histogram',
      values: macd.histogram,
      color: '#10B981',
      type: 'histogram'
    });

    // Bollinger Bands
    const bollinger = this.calculateBollingerBands(candlesticks, 20, 2);
    indicators.push({
      name: 'Bollinger Upper',
      values: bollinger.upper,
      color: '#F59E0B',
      type: 'line'
    });

    indicators.push({
      name: 'Bollinger Middle',
      values: bollinger.middle,
      color: '#6B7280',
      type: 'line'
    });

    indicators.push({
      name: 'Bollinger Lower',
      values: bollinger.lower,
      color: '#F59E0B',
      type: 'line'
    });

    // EMA
    const ema20 = this.calculateEMA(candlesticks, 20);
    const ema50 = this.calculateEMA(candlesticks, 50);
    
    indicators.push({
      name: 'EMA 20',
      values: ema20,
      color: '#06B6D4',
      type: 'line'
    });

    indicators.push({
      name: 'EMA 50',
      values: ema50,
      color: '#8B5CF6',
      type: 'line'
    });

    return indicators;
  }

  // حساب RSI
  private calculateRSI(candlesticks: CandlestickData[], period: number): number[] {
    const rsi: number[] = [];
    const gains: number[] = [];
    const losses: number[] = [];

    for (let i = 1; i < candlesticks.length; i++) {
      const change = candlesticks[i].close - candlesticks[i - 1].close;
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }

    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((sum, gain) => sum + gain, 0) / period;
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((sum, loss) => sum + loss, 0) / period;
      
      if (avgLoss === 0) {
        rsi.push(100);
      } else {
        const rs = avgGain / avgLoss;
        rsi.push(100 - (100 / (1 + rs)));
      }
    }

    // ملء القيم المفقودة في البداية
    const fillValue = rsi[0] || 50;
    return new Array(period - 1).fill(fillValue).concat(rsi);
  }

  // حساب MACD
  private calculateMACD(candlesticks: CandlestickData[]): {
    macd: number[];
    signal: number[];
    histogram: number[];
  } {
    const ema12 = this.calculateEMA(candlesticks, 12);
    const ema26 = this.calculateEMA(candlesticks, 26);
    
    const macd = ema12.map((val, i) => val - ema26[i]);
    const signal = this.calculateEMAFromArray(macd, 9);
    const histogram = macd.map((val, i) => val - signal[i]);

    return { macd, signal, histogram };
  }

  // حساب Bollinger Bands
  private calculateBollingerBands(
    candlesticks: CandlestickData[], 
    period: number, 
    multiplier: number
  ): { upper: number[]; middle: number[]; lower: number[] } {
    const closes = candlesticks.map(c => c.close);
    const sma = this.calculateSMA(closes, period);
    const upper: number[] = [];
    const lower: number[] = [];

    for (let i = period - 1; i < closes.length; i++) {
      const slice = closes.slice(i - period + 1, i + 1);
      const mean = sma[i];
      const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
      const stdDev = Math.sqrt(variance);
      
      upper.push(mean + (stdDev * multiplier));
      lower.push(mean - (stdDev * multiplier));
    }

    // ملء القيم المفقودة
    const fillUpper = upper[0] || sma[0] * 1.02;
    const fillLower = lower[0] || sma[0] * 0.98;
    
    return {
      upper: new Array(period - 1).fill(fillUpper).concat(upper),
      middle: sma,
      lower: new Array(period - 1).fill(fillLower).concat(lower)
    };
  }

  // حساب EMA
  private calculateEMA(candlesticks: CandlestickData[], period: number): number[] {
    const closes = candlesticks.map(c => c.close);
    return this.calculateEMAFromArray(closes, period);
  }

  private calculateEMAFromArray(values: number[], period: number): number[] {
    const ema: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // أول قيمة هي SMA
    let sum = 0;
    for (let i = 0; i < period && i < values.length; i++) {
      sum += values[i];
    }
    ema.push(sum / Math.min(period, values.length));

    // باقي القيم
    for (let i = 1; i < values.length; i++) {
      const currentEMA = (values[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
      ema.push(currentEMA);
    }

    return ema;
  }

  // حساب SMA
  private calculateSMA(values: number[], period: number): number[] {
    const sma: number[] = [];
    
    for (let i = period - 1; i < values.length; i++) {
      const slice = values.slice(i - period + 1, i + 1);
      const average = slice.reduce((sum, val) => sum + val, 0) / period;
      sma.push(average);
    }

    // ملء القيم المفقودة في البداية
    const fillValue = sma[0] || values[0] || 0;
    return new Array(period - 1).fill(fillValue).concat(sma);
  }

  // دوال مساعدة
  private getBasePrice(symbol: string): number {
    const prices: Record<string, number> = {
      'EURUSD': 1.0850,
      'GBPUSD': 1.2650,
      'USDJPY': 149.50,
      'AUDUSD': 0.6750,
      'USDCAD': 1.3450,
      'BTCUSDT': 43500,
      'ETHUSDT': 2650,
      'XAUUSD': 2050
    };
    return prices[symbol] || 1.0000;
  }

  private getVolatility(symbol: string): number {
    const volatilities: Record<string, number> = {
      'EURUSD': 0.008,
      'GBPUSD': 0.012,
      'USDJPY': 0.010,
      'AUDUSD': 0.015,
      'USDCAD': 0.009,
      'BTCUSDT': 0.035,
      'ETHUSDT': 0.040,
      'XAUUSD': 0.018
    };
    return volatilities[symbol] || 0.010;
  }

  private getBaseVolume(symbol: string): number {
    const volumes: Record<string, number> = {
      'EURUSD': 1500000,
      'GBPUSD': 1200000,
      'USDJPY': 1800000,
      'AUDUSD': 800000,
      'USDCAD': 600000,
      'BTCUSDT': 25000000,
      'ETHUSDT': 15000000,
      'XAUUSD': 500000
    };
    return volumes[symbol] || 1000000;
  }

  private calculateTrend(index: number, total: number): number {
    // محاكاة اتجاه السوق
    const position = index / total;
    const cycles = Math.sin(position * Math.PI * 4) * 0.002;
    const trend = (Math.random() - 0.5) * 0.001;
    return cycles + trend;
  }

  private getTimeframeMs(timeframe: string): number {
    const timeframes: Record<string, number> = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000
    };
    return timeframes[timeframe] || 60 * 60 * 1000;
  }

  // الحصول على بيانات السوق الكاملة
  async getMarketData(symbol: string, timeframe: string): Promise<MarketData> {
    const cacheKey = `${symbol}_${timeframe}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const candlesticks = this.generateCandlestickData(symbol, timeframe);
    const indicators = this.calculateTechnicalIndicators(candlesticks);
    
    const supportLevels = this.findSupportLevels(candlesticks);
    const resistanceLevels = this.findResistanceLevels(candlesticks);
    const trendLines = this.generateTrendLines(candlesticks);

    const marketData: MarketData = {
      symbol,
      timeframe,
      candlesticks,
      indicators,
      supportLevels,
      resistanceLevels,
      trendLines
    };

    this.cache.set(cacheKey, marketData);
    return marketData;
  }

  // العثور على مستويات الدعم
  private findSupportLevels(candlesticks: CandlestickData[]): number[] {
    const lows = candlesticks.map(c => c.low);
    const supports: number[] = [];
    
    // البحث عن القيع المحلية
    for (let i = 2; i < lows.length - 2; i++) {
      if (lows[i] < lows[i-1] && lows[i] < lows[i-2] && 
          lows[i] < lows[i+1] && lows[i] < lows[i+2]) {
        supports.push(lows[i]);
      }
    }
    
    // ترتيب وإرجاع أقوى مستويات الدعم
    return supports.sort((a, b) => a - b).slice(0, 3);
  }

  // العثور على مستويات المقاومة
  private findResistanceLevels(candlesticks: CandlestickData[]): number[] {
    const highs = candlesticks.map(c => c.high);
    const resistances: number[] = [];
    
    // البحث عن القمم المحلية
    for (let i = 2; i < highs.length - 2; i++) {
      if (highs[i] > highs[i-1] && highs[i] > highs[i-2] && 
          highs[i] > highs[i+1] && highs[i] > highs[i+2]) {
        resistances.push(highs[i]);
      }
    }
    
    // ترتيب وإرجاع أقوى مستويات المقاومة
    return resistances.sort((a, b) => b - a).slice(0, 3);
  }

  // توليد خطوط الاتجاه
  private generateTrendLines(candlesticks: CandlestickData[]): MarketData['trendLines'] {
    const trendLines: MarketData['trendLines'] = [];
    
    // خط اتجاه صاعد
    const upTrendStart = Math.floor(candlesticks.length * 0.2);
    const upTrendEnd = Math.floor(candlesticks.length * 0.8);
    
    trendLines.push({
      start: { 
        x: candlesticks[upTrendStart].timestamp, 
        y: candlesticks[upTrendStart].low 
      },
      end: { 
        x: candlesticks[upTrendEnd].timestamp, 
        y: candlesticks[upTrendEnd].low * 1.05 
      },
      type: 'trend'
    });

    return trendLines;
  }

  // الحصول على الرموز المتاحة
  getAvailableSymbols(): string[] {
    return this.symbols;
  }

  // الحصول على الأطر الزمنية المتاحة
  getAvailableTimeframes(): string[] {
    return this.timeframes;
  }

  // تحديث البيانات
  async refreshData(symbol: string, timeframe: string): Promise<MarketData> {
    const cacheKey = `${symbol}_${timeframe}`;
    this.cache.delete(cacheKey);
    return this.getMarketData(symbol, timeframe);
  }
}

export const enhancedMarketDataService = new EnhancedMarketDataService();
